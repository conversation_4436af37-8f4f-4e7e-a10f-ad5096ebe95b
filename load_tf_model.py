import tensorflow as tf
import numpy as np
import os

def load_and_use_model(model_path, input_data=None):
    """
    加载并使用TensorFlow模型
    
    参数:
        model_path: 模型文件路径
        input_data: 用于预测的输入数据，如果为None则生成随机测试数据
    
    返回:
        模型预测结果
    """
    try:
        # 检查模型文件是否存在
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
            
        print(f"正在加载模型: {model_path}")
        
        # 加载模型
        model = tf.keras.models.load_model(model_path)
        
        # 打印模型摘要
        model.summary()
        
        # 如果没有提供输入数据，创建一些随机测试数据
        if input_data is None:
            # 获取模型输入形状
            input_shape = model.input_shape
            if isinstance(input_shape, list):
                # 多输入模型
                input_data = [np.random.random(shape[1:]).reshape(1, *shape[1:]) 
                             for shape in input_shape if shape is not None]
            else:
                # 单输入模型
                batch_shape = input_shape[1:]
                input_data = np.random.random(batch_shape).reshape(1, *batch_shape)
            print("使用随机生成的测试数据")
        
        # 进行预测
        predictions = model.predict(input_data)
        print("预测完成")
        
        return predictions
        
    except Exception as e:
        print(f"加载或使用模型时出错: {str(e)}")
        raise

if __name__ == "__main__":
    # 模型路径
    model_path = r"D:\模型开发\tf_dementia_model.h5"
    
    # 加载并使用模型
    predictions = load_and_use_model(model_path)
    
    # 打印预测结果
    print("\n预测结果:")
    if isinstance(predictions, list):
        for i, pred in enumerate(predictions):
            print(f"输出 {i+1}:", pred)
    else:
        # 如果是二分类问题，显示概率
        if predictions.shape[-1] == 1:
            print(f"痴呆症概率: {predictions.flatten()}")
        # 如果是多分类问题，显示类别概率
        elif predictions.shape[-1] > 1:
            class_names = ["正常", "轻度认知障碍", "痴呆症"]  # 根据您的模型调整类别名称
            for i, pred in enumerate(predictions[0]):
                if i < len(class_names):
                    print(f"{class_names[i]}: {pred:.4f} ({pred*100:.2f}%)")
                else:
                    print(f"类别 {i}: {pred:.4f} ({pred*100:.2f}%)")
        else:
            print(predictions)