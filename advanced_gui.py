#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级痴呆症检测工具 - 专业GUI版本
包含历史记录、报告导出、批量检测等高级功能
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import os
import json
import datetime
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

class AdvancedDementiaGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("AI痴呆症检测系统 - 专业版 v2.0")
        self.root.geometry("900x800")
        self.root.configure(bg='#f5f5f5')
        
        # 数据存储
        self.history_file = "detection_history.json"
        self.detection_history = self.load_history()
        
        # 创建界面
        self.create_menu()
        self.create_main_interface()
        
        # 模型状态
        self.model_loaded = False
        
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导出报告", command=self.export_report)
        file_menu.add_command(label="批量检测", command=self.batch_detection)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="清除历史", command=self.clear_history)
        tools_menu.add_command(label="创建测试音频", command=self.create_test_audio)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def create_main_interface(self):
        """创建主界面"""
        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 检测页面
        self.detection_frame = ttk.Frame(notebook)
        notebook.add(self.detection_frame, text="🔍 AI检测")
        self.create_detection_tab()
        
        # 历史记录页面
        self.history_frame = ttk.Frame(notebook)
        notebook.add(self.history_frame, text="📊 历史记录")
        self.create_history_tab()
        
        # 统计分析页面
        self.stats_frame = ttk.Frame(notebook)
        notebook.add(self.stats_frame, text="📈 统计分析")
        self.create_stats_tab()
    
    def create_detection_tab(self):
        """创建检测标签页"""
        # 主标题
        title_frame = tk.Frame(self.detection_frame, bg='#f5f5f5')
        title_frame.pack(pady=20)
        
        title_label = tk.Label(title_frame, text="🧠 AI痴呆症检测系统", 
                              font=('Arial', 18, 'bold'), bg='#f5f5f5', fg='#2c3e50')
        title_label.pack()
        
        subtitle_label = tk.Label(title_frame, text="基于深度学习的多模态认知功能评估", 
                                 font=('Arial', 12), bg='#f5f5f5', fg='#7f8c8d')
        subtitle_label.pack(pady=5)
        
        # 输入区域
        input_frame = tk.LabelFrame(self.detection_frame, text="📝 患者信息", 
                                   font=('Arial', 12, 'bold'), bg='#f5f5f5', padx=20, pady=15)
        input_frame.pack(fill='x', padx=20, pady=10)
        
        # 创建输入字段
        self.create_input_fields(input_frame)
        
        # 音频区域
        audio_frame = tk.LabelFrame(self.detection_frame, text="🎵 语音分析 (可选)", 
                                   font=('Arial', 12, 'bold'), bg='#f5f5f5', padx=20, pady=15)
        audio_frame.pack(fill='x', padx=20, pady=10)
        self.create_audio_section(audio_frame)
        
        # 控制按钮
        control_frame = tk.Frame(self.detection_frame, bg='#f5f5f5')
        control_frame.pack(pady=20)
        
        self.analyze_btn = tk.Button(control_frame, text="🔍 开始AI分析", 
                                    command=self.start_analysis,
                                    font=('Arial', 14, 'bold'), bg='#3498db', fg='white', 
                                    padx=40, pady=12, relief='raised', bd=3)
        self.analyze_btn.pack(side='left', padx=10)
        
        clear_btn = tk.Button(control_frame, text="🗑️ 清空", command=self.clear_inputs,
                             font=('Arial', 12), bg='#e74c3c', fg='white', 
                             padx=20, pady=12, relief='raised', bd=3)
        clear_btn.pack(side='left', padx=10)
        
        # 进度条
        self.progress = ttk.Progressbar(self.detection_frame, mode='indeterminate', length=400)
        self.progress.pack(pady=10)
        
        # 结果显示
        self.create_result_section()
    
    def create_input_fields(self, parent):
        """创建输入字段"""
        # 患者姓名
        name_frame = tk.Frame(parent, bg='#f5f5f5')
        name_frame.pack(fill='x', pady=5)
        tk.Label(name_frame, text="姓名:", font=('Arial', 11), bg='#f5f5f5', width=12, anchor='w').pack(side='left')
        self.name_var = tk.StringVar()
        tk.Entry(name_frame, textvariable=self.name_var, font=('Arial', 11), width=20).pack(side='left', padx=5)
        
        # 年龄
        age_frame = tk.Frame(parent, bg='#f5f5f5')
        age_frame.pack(fill='x', pady=5)
        tk.Label(age_frame, text="年龄:", font=('Arial', 11), bg='#f5f5f5', width=12, anchor='w').pack(side='left')
        self.age_var = tk.StringVar()
        age_spinbox = tk.Spinbox(age_frame, textvariable=self.age_var, from_=0, to=120, 
                                font=('Arial', 11), width=10)
        age_spinbox.pack(side='left', padx=5)
        tk.Label(age_frame, text="岁", font=('Arial', 11), bg='#f5f5f5').pack(side='left')
        
        # 性别
        gender_frame = tk.Frame(parent, bg='#f5f5f5')
        gender_frame.pack(fill='x', pady=5)
        tk.Label(gender_frame, text="性别:", font=('Arial', 11), bg='#f5f5f5', width=12, anchor='w').pack(side='left')
        self.gender_var = tk.StringVar(value="M")
        tk.Radiobutton(gender_frame, text="👨 男性", variable=self.gender_var, value="M", 
                      font=('Arial', 11), bg='#f5f5f5').pack(side='left', padx=10)
        tk.Radiobutton(gender_frame, text="👩 女性", variable=self.gender_var, value="F", 
                      font=('Arial', 11), bg='#f5f5f5').pack(side='left', padx=10)
        
        # 教育年限
        edu_frame = tk.Frame(parent, bg='#f5f5f5')
        edu_frame.pack(fill='x', pady=5)
        tk.Label(edu_frame, text="教育年限:", font=('Arial', 11), bg='#f5f5f5', width=12, anchor='w').pack(side='left')
        self.education_var = tk.StringVar()
        edu_spinbox = tk.Spinbox(edu_frame, textvariable=self.education_var, from_=0, to=30, 
                                font=('Arial', 11), width=10)
        edu_spinbox.pack(side='left', padx=5)
        tk.Label(edu_frame, text="年", font=('Arial', 11), bg='#f5f5f5').pack(side='left')
        
        # 快速填充按钮
        quick_frame = tk.Frame(parent, bg='#f5f5f5')
        quick_frame.pack(fill='x', pady=10)
        tk.Label(quick_frame, text="快速填充:", font=('Arial', 11), bg='#f5f5f5', width=12, anchor='w').pack(side='left')
        
        examples = [
            ("示例1", "张三", "90", "M", "20"),
            ("示例2", "李四", "75", "F", "16"),
            ("示例3", "王五", "65", "M", "12")
        ]
        
        for desc, name, age, gender, edu in examples:
            btn = tk.Button(quick_frame, text=desc, 
                           command=lambda n=name, a=age, g=gender, e=edu: self.load_example(n, a, g, e),
                           font=('Arial', 9), bg='#95a5a6', fg='white', padx=8, pady=2)
            btn.pack(side='left', padx=2)
    
    def create_audio_section(self, parent):
        """创建音频选择区域"""
        audio_select_frame = tk.Frame(parent, bg='#f5f5f5')
        audio_select_frame.pack(fill='x', pady=5)
        
        self.audio_path_var = tk.StringVar()
        audio_entry = tk.Entry(audio_select_frame, textvariable=self.audio_path_var, 
                              font=('Arial', 10), state='readonly', bg='white')
        audio_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))
        
        browse_btn = tk.Button(audio_select_frame, text="📁 选择音频", command=self.browse_audio_file,
                              font=('Arial', 10), bg='#27ae60', fg='white', padx=15, pady=5)
        browse_btn.pack(side='right', padx=2)
        
        clear_btn = tk.Button(audio_select_frame, text="❌ 清除", command=self.clear_audio_file,
                             font=('Arial', 10), bg='#e74c3c', fg='white', padx=15, pady=5)
        clear_btn.pack(side='right', padx=2)
        
        record_btn = tk.Button(audio_select_frame, text="🎤 录音", command=self.record_audio,
                              font=('Arial', 10), bg='#f39c12', fg='white', padx=15, pady=5)
        record_btn.pack(side='right', padx=2)
    
    def create_result_section(self):
        """创建结果显示区域"""
        self.result_frame = tk.LabelFrame(self.detection_frame, text="📊 检测结果", 
                                         font=('Arial', 12, 'bold'), bg='#f5f5f5', padx=20, pady=15)
        self.result_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # 结果文本框
        text_frame = tk.Frame(self.result_frame, bg='#f5f5f5')
        text_frame.pack(fill='both', expand=True)
        
        self.result_text = tk.Text(text_frame, height=10, font=('Arial', 11), 
                                  bg='white', relief='sunken', bd=2, wrap='word')
        self.result_text.pack(side='left', fill='both', expand=True)
        
        # 滚动条
        scrollbar = tk.Scrollbar(text_frame)
        scrollbar.pack(side='right', fill='y')
        self.result_text.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.result_text.yview)
        
        # 操作按钮
        button_frame = tk.Frame(self.result_frame, bg='#f5f5f5')
        button_frame.pack(fill='x', pady=10)
        
        save_btn = tk.Button(button_frame, text="💾 保存结果", command=self.save_current_result,
                            font=('Arial', 10), bg='#2ecc71', fg='white', padx=15, pady=5)
        save_btn.pack(side='left', padx=5)
        
        export_btn = tk.Button(button_frame, text="📄 导出PDF", command=self.export_pdf,
                              font=('Arial', 10), bg='#9b59b6', fg='white', padx=15, pady=5)
        export_btn.pack(side='left', padx=5)
        
        # 状态栏
        self.status_var = tk.StringVar(value="系统就绪 - 请输入患者信息")
        status_bar = tk.Label(self.detection_frame, textvariable=self.status_var, relief='sunken', 
                             anchor='w', font=('Arial', 10), bg='#ecf0f1', fg='#2c3e50')
        status_bar.pack(fill='x', side='bottom', padx=20)

    def create_history_tab(self):
        """创建历史记录标签页"""
        # 历史记录列表
        history_frame = tk.Frame(self.history_frame, bg='#f5f5f5')
        history_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # 表格
        columns = ('时间', '姓名', '年龄', '性别', '教育', '结果', '置信度')
        self.history_tree = ttk.Treeview(history_frame, columns=columns, show='headings', height=15)

        for col in columns:
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=100)

        self.history_tree.pack(side='left', fill='both', expand=True)

        # 滚动条
        history_scrollbar = ttk.Scrollbar(history_frame, orient='vertical', command=self.history_tree.yview)
        history_scrollbar.pack(side='right', fill='y')
        self.history_tree.configure(yscrollcommand=history_scrollbar.set)

        # 刷新历史记录
        self.refresh_history()

        # 操作按钮
        history_btn_frame = tk.Frame(self.history_frame, bg='#f5f5f5')
        history_btn_frame.pack(fill='x', padx=20, pady=10)

        refresh_btn = tk.Button(history_btn_frame, text="🔄 刷新", command=self.refresh_history,
                               font=('Arial', 10), bg='#3498db', fg='white', padx=15, pady=5)
        refresh_btn.pack(side='left', padx=5)

        delete_btn = tk.Button(history_btn_frame, text="🗑️ 删除选中", command=self.delete_selected,
                              font=('Arial', 10), bg='#e74c3c', fg='white', padx=15, pady=5)
        delete_btn.pack(side='left', padx=5)

    def create_stats_tab(self):
        """创建统计分析标签页"""
        stats_label = tk.Label(self.stats_frame, text="📈 统计分析功能开发中...",
                              font=('Arial', 16), bg='#f5f5f5')
        stats_label.pack(expand=True)

    # 功能方法
    def load_history(self):
        """加载历史记录"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except:
            pass
        return []

    def save_history(self):
        """保存历史记录"""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.detection_history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            messagebox.showerror("保存错误", f"无法保存历史记录: {e}")

    def browse_audio_file(self):
        """选择音频文件"""
        file_path = filedialog.askopenfilename(
            title="选择音频文件",
            filetypes=[("WAV files", "*.wav"), ("MP3 files", "*.mp3"), ("All files", "*.*")]
        )
        if file_path:
            self.audio_path_var.set(file_path)
            self.status_var.set(f"已选择音频: {os.path.basename(file_path)}")

    def clear_audio_file(self):
        """清除音频文件"""
        self.audio_path_var.set("")
        self.status_var.set("已清除音频文件")

    def record_audio(self):
        """录音功能（占位）"""
        messagebox.showinfo("录音功能", "录音功能开发中，请先选择现有音频文件")

    def load_example(self, name, age, gender, education):
        """加载示例数据"""
        self.name_var.set(name)
        self.age_var.set(age)
        self.gender_var.set(gender)
        self.education_var.set(education)
        self.status_var.set(f"已加载示例: {name}")

    def clear_inputs(self):
        """清空所有输入"""
        self.name_var.set("")
        self.age_var.set("")
        self.education_var.set("")
        self.audio_path_var.set("")
        self.result_text.delete(1.0, tk.END)
        self.status_var.set("已清空所有输入")

    def validate_input(self):
        """验证输入"""
        if not self.name_var.get().strip():
            messagebox.showerror("输入错误", "请输入患者姓名")
            return False

        try:
            age = int(self.age_var.get())
            if age < 0 or age > 120:
                raise ValueError()
        except:
            messagebox.showerror("输入错误", "请输入有效年龄（0-120）")
            return False

        try:
            education = int(self.education_var.get())
            if education < 0 or education > 30:
                raise ValueError()
        except:
            messagebox.showerror("输入错误", "请输入有效教育年限（0-30）")
            return False

        return True

    def start_analysis(self):
        """开始分析"""
        if not self.validate_input():
            return

        self.analyze_btn.config(state='disabled')
        self.progress.start()
        self.status_var.set("正在进行AI分析...")

        thread = threading.Thread(target=self.run_analysis)
        thread.daemon = True
        thread.start()

    def run_analysis(self):
        """运行分析"""
        try:
            name = self.name_var.get().strip()
            age = int(self.age_var.get())
            gender = self.gender_var.get()
            education = int(self.education_var.get())
            audio_file = self.audio_path_var.get() if self.audio_path_var.get() else None

            self.root.after(0, lambda: self.status_var.set("正在加载AI模型..."))

            from easy_predict import detailed_predict

            self.root.after(0, lambda: self.status_var.set("AI分析中..."))

            result = detailed_predict(age=age, gender=gender, education_years=education, audio_file=audio_file)

            # 保存到历史记录
            record = {
                'timestamp': datetime.datetime.now().isoformat(),
                'name': name,
                'age': age,
                'gender': gender,
                'education': education,
                'audio_used': audio_file is not None and result.get('has_audio', False),
                'result': result
            }

            self.detection_history.append(record)
            self.save_history()

            self.root.after(0, lambda: self.display_result(result, record))

        except Exception as e:
            self.root.after(0, lambda: self.display_error(str(e)))

    def display_result(self, result, record):
        """显示结果"""
        self.progress.stop()
        self.analyze_btn.config(state='normal')

        self.result_text.delete(1.0, tk.END)

        # 格式化结果
        timestamp = datetime.datetime.fromisoformat(record['timestamp']).strftime('%Y-%m-%d %H:%M:%S')

        result_text = f"""
🔍 AI检测报告

📋 患者信息:
   姓名: {record['name']}
   年龄: {record['age']}岁
   性别: {'男性' if record['gender'] == 'M' else '女性'}
   教育年限: {record['education']}年
   检测时间: {timestamp}
   语音分析: {'已使用' if record['audio_used'] else '未使用'}

🎯 检测结果:
   预测类别: {result['predicted_class']}
   置信度: {result['confidence']:.1%}

📊 详细概率分布:
   正常 (Normal): {result['probabilities']['Normal']:.3f} ({result['probabilities']['Normal']*100:.1f}%)
   轻度认知障碍 (MCI): {result['probabilities']['MCI']:.3f} ({result['probabilities']['MCI']*100:.1f}%)
   痴呆症 (Dementia): {result['probabilities']['Dementia']:.3f} ({result['probabilities']['Dementia']*100:.1f}%)

💡 专业建议:
"""

        if result['predicted_class'] == 'Normal':
            result_text += """   ✅ 认知功能正常
   • 继续保持健康的生活方式
   • 定期进行认知功能检查
   • 保持适度运动和社交活动"""
        elif result['predicted_class'] == 'MCI':
            result_text += """   ⚠️ 轻度认知障碍
   • 建议咨询神经科医生
   • 进行更详细的认知评估
   • 考虑认知训练和生活方式干预"""
        else:
            result_text += """   🔴 痴呆症风险较高
   • 强烈建议尽快就医
   • 需要专业医生进一步诊断
   • 考虑家庭支持和护理计划"""

        result_text += "\n\n📝 重要声明:\n   此结果仅供参考，不能替代专业医学诊断。\n   如有疑虑，请及时咨询专业医生。"

        self.result_text.insert(tk.END, result_text)

        # 刷新历史记录
        self.refresh_history()

        self.status_var.set("检测完成")

    def display_error(self, error_msg):
        """显示错误"""
        self.progress.stop()
        self.analyze_btn.config(state='normal')
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, f"❌ 检测失败: {error_msg}")
        self.status_var.set("检测失败")
        messagebox.showerror("检测错误", error_msg)

    def refresh_history(self):
        """刷新历史记录"""
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)

        for record in reversed(self.detection_history[-50:]):  # 显示最近50条
            timestamp = datetime.datetime.fromisoformat(record['timestamp']).strftime('%m-%d %H:%M')
            values = (
                timestamp,
                record['name'],
                record['age'],
                '男' if record['gender'] == 'M' else '女',
                record['education'],
                record['result']['predicted_class'],
                f"{record['result']['confidence']:.1%}"
            )
            self.history_tree.insert('', 0, values=values)

    def delete_selected(self):
        """删除选中的历史记录"""
        selected = self.history_tree.selection()
        if not selected:
            messagebox.showwarning("选择错误", "请先选择要删除的记录")
            return

        if messagebox.askyesno("确认删除", "确定要删除选中的记录吗？"):
            # 这里需要实现删除逻辑
            messagebox.showinfo("删除", "删除功能开发中")

    # 菜单功能
    def export_report(self):
        """导出报告"""
        messagebox.showinfo("导出报告", "报告导出功能开发中")

    def batch_detection(self):
        """批量检测"""
        messagebox.showinfo("批量检测", "批量检测功能开发中")

    def clear_history(self):
        """清除历史"""
        if messagebox.askyesno("确认清除", "确定要清除所有历史记录吗？"):
            self.detection_history = []
            self.save_history()
            self.refresh_history()
            messagebox.showinfo("清除完成", "历史记录已清除")

    def create_test_audio(self):
        """创建测试音频"""
        try:
            import subprocess
            subprocess.run(["python", "create_test_audio.py"], check=True)
            messagebox.showinfo("创建成功", "测试音频文件已创建: test_audio.wav")
        except:
            messagebox.showerror("创建失败", "无法创建测试音频文件")

    def save_current_result(self):
        """保存当前结果"""
        content = self.result_text.get(1.0, tk.END)
        if not content.strip():
            messagebox.showwarning("保存错误", "没有结果可保存")
            return

        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("保存成功", f"结果已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("保存失败", f"保存失败: {e}")

    def export_pdf(self):
        """导出PDF"""
        messagebox.showinfo("导出PDF", "PDF导出功能开发中")

    def show_help(self):
        """显示帮助"""
        help_text = """
🔍 AI痴呆症检测系统使用说明

1. 输入患者基本信息（姓名、年龄、性别、教育年限）
2. 可选择上传语音文件进行多模态分析
3. 点击"开始AI分析"进行检测
4. 查看详细的检测结果和建议
5. 结果会自动保存到历史记录中

注意事项:
• 此系统仅供辅助参考，不能替代专业医学诊断
• 建议结合临床检查和专业医生意见
• 语音文件推荐使用WAV格式
        """
        messagebox.showinfo("使用说明", help_text)

    def show_about(self):
        """显示关于"""
        about_text = """
🧠 AI痴呆症检测系统 v2.0

基于深度学习的多模态认知功能评估工具

开发: AI医疗团队
技术: TensorFlow + 多模态融合
功能: 认知功能评估、语音分析、历史记录

⚠️ 仅供医疗辅助参考使用
        """
        messagebox.showinfo("关于系统", about_text)

def main():
    """主函数"""
    root = tk.Tk()
    app = AdvancedDementiaGUI(root)

    # 设置窗口居中
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")

    root.mainloop()

if __name__ == "__main__":
    main()
