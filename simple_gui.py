#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版痴呆症检测GUI - 易用高效
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import os
import warnings
warnings.filterwarnings('ignore')
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

class SimpleDementiaGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🧠 AI痴呆症检测工具")
        self.root.geometry("700x600")
        self.root.configure(bg='#ffffff')
        
        # 设置样式
        self.setup_styles()
        self.create_interface()
        
    def setup_styles(self):
        """设置样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
    def create_interface(self):
        """创建界面"""
        # 标题
        title_frame = tk.Frame(self.root, bg='#ffffff', pady=20)
        title_frame.pack(fill='x')
        
        title = tk.Label(title_frame, text="🧠 AI痴呆症检测系统", 
                        font=('Arial', 20, 'bold'), bg='#ffffff', fg='#2c3e50')
        title.pack()
        
        subtitle = tk.Label(title_frame, text="智能认知功能评估工具", 
                           font=('Arial', 12), bg='#ffffff', fg='#7f8c8d')
        subtitle.pack(pady=5)
        
        # 主容器
        main_frame = tk.Frame(self.root, bg='#ffffff')
        main_frame.pack(fill='both', expand=True, padx=30, pady=20)
        
        # 输入区域
        input_frame = tk.LabelFrame(main_frame, text="  📝 患者信息  ", 
                                   font=('Arial', 12, 'bold'), bg='#ffffff', 
                                   relief='groove', bd=2, padx=20, pady=15)
        input_frame.pack(fill='x', pady=10)
        
        # 姓名
        self.create_input_row(input_frame, "姓名:", "name_var", width=20)
        
        # 年龄
        self.create_input_row(input_frame, "年龄:", "age_var", width=10, suffix="岁")
        
        # 性别
        gender_frame = tk.Frame(input_frame, bg='#ffffff')
        gender_frame.pack(fill='x', pady=8)
        tk.Label(gender_frame, text="性别:", font=('Arial', 12), bg='#ffffff', width=8, anchor='w').pack(side='left')
        
        self.gender_var = tk.StringVar(value="M")
        tk.Radiobutton(gender_frame, text="👨 男", variable=self.gender_var, value="M", 
                      font=('Arial', 12), bg='#ffffff', selectcolor='#e3f2fd').pack(side='left', padx=10)
        tk.Radiobutton(gender_frame, text="👩 女", variable=self.gender_var, value="F", 
                      font=('Arial', 12), bg='#ffffff', selectcolor='#fce4ec').pack(side='left', padx=10)
        
        # 教育年限
        self.create_input_row(input_frame, "教育年限:", "education_var", width=10, suffix="年")
        
        # 快速示例
        example_frame = tk.Frame(input_frame, bg='#ffffff')
        example_frame.pack(fill='x', pady=10)
        tk.Label(example_frame, text="快速填充:", font=('Arial', 11), bg='#ffffff', width=8, anchor='w').pack(side='left')
        
        examples = [
            ("示例1", "张先生", "90", "M", "20"),
            ("示例2", "李女士", "75", "F", "16"),
            ("示例3", "王先生", "65", "M", "12")
        ]
        
        for desc, name, age, gender, edu in examples:
            btn = tk.Button(example_frame, text=desc, 
                           command=lambda n=name, a=age, g=gender, e=edu: self.load_example(n, a, g, e),
                           font=('Arial', 10), bg='#e8f5e8', relief='raised', bd=1, padx=8, pady=2)
            btn.pack(side='left', padx=3)
        
        # 音频区域
        audio_frame = tk.LabelFrame(main_frame, text="  🎵 语音文件 (可选)  ", 
                                   font=('Arial', 12, 'bold'), bg='#ffffff', 
                                   relief='groove', bd=2, padx=20, pady=15)
        audio_frame.pack(fill='x', pady=10)
        
        audio_select_frame = tk.Frame(audio_frame, bg='#ffffff')
        audio_select_frame.pack(fill='x')
        
        self.audio_path_var = tk.StringVar()
        audio_entry = tk.Entry(audio_select_frame, textvariable=self.audio_path_var, 
                              font=('Arial', 11), state='readonly', bg='#f8f9fa', relief='sunken', bd=1)
        audio_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))
        
        browse_btn = tk.Button(audio_select_frame, text="📁 选择", command=self.browse_audio,
                              font=('Arial', 10), bg='#4CAF50', fg='white', padx=15, pady=5, relief='raised', bd=2)
        browse_btn.pack(side='right', padx=2)
        
        clear_btn = tk.Button(audio_select_frame, text="❌ 清除", command=self.clear_audio,
                             font=('Arial', 10), bg='#f44336', fg='white', padx=15, pady=5, relief='raised', bd=2)
        clear_btn.pack(side='right', padx=2)
        
        # 控制按钮
        control_frame = tk.Frame(main_frame, bg='#ffffff')
        control_frame.pack(pady=20)
        
        self.analyze_btn = tk.Button(control_frame, text="🔍 开始AI检测", 
                                    command=self.start_analysis,
                                    font=('Arial', 16, 'bold'), bg='#2196F3', fg='white', 
                                    padx=40, pady=15, relief='raised', bd=3)
        self.analyze_btn.pack(side='left', padx=10)
        
        clear_all_btn = tk.Button(control_frame, text="🗑️ 清空", command=self.clear_all,
                                 font=('Arial', 12), bg='#FF5722', fg='white', 
                                 padx=20, pady=15, relief='raised', bd=3)
        clear_all_btn.pack(side='left', padx=10)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate', length=500)
        self.progress.pack(pady=10)
        
        # 结果区域
        result_frame = tk.LabelFrame(main_frame, text="  📊 检测结果  ", 
                                    font=('Arial', 12, 'bold'), bg='#ffffff', 
                                    relief='groove', bd=2, padx=20, pady=15)
        result_frame.pack(fill='both', expand=True, pady=10)
        
        # 结果显示
        text_frame = tk.Frame(result_frame, bg='#ffffff')
        text_frame.pack(fill='both', expand=True)
        
        self.result_text = tk.Text(text_frame, height=12, font=('Arial', 11), 
                                  bg='#fafafa', relief='sunken', bd=2, wrap='word')
        self.result_text.pack(side='left', fill='both', expand=True)
        
        scrollbar = tk.Scrollbar(text_frame, command=self.result_text.yview)
        scrollbar.pack(side='right', fill='y')
        self.result_text.config(yscrollcommand=scrollbar.set)
        
        # 状态栏
        self.status_var = tk.StringVar(value="系统就绪 - 请输入患者信息并开始检测")
        status_bar = tk.Label(self.root, textvariable=self.status_var, relief='sunken', 
                             anchor='w', font=('Arial', 10), bg='#f0f0f0', fg='#333333')
        status_bar.pack(fill='x', side='bottom')
        
        # 初始化变量
        self.name_var = tk.StringVar()
        self.age_var = tk.StringVar()
        self.education_var = tk.StringVar()
        
        # 绑定变量到输入框
        self.bind_variables(input_frame)
    
    def create_input_row(self, parent, label_text, var_name, width=15, suffix=""):
        """创建输入行"""
        frame = tk.Frame(parent, bg='#ffffff')
        frame.pack(fill='x', pady=8)
        
        tk.Label(frame, text=label_text, font=('Arial', 12), bg='#ffffff', width=8, anchor='w').pack(side='left')
        
        var = tk.StringVar()
        setattr(self, var_name, var)
        
        entry = tk.Entry(frame, textvariable=var, font=('Arial', 12), width=width, 
                        relief='sunken', bd=1, bg='white')
        entry.pack(side='left', padx=5)
        
        if suffix:
            tk.Label(frame, text=suffix, font=('Arial', 12), bg='#ffffff').pack(side='left')
    
    def bind_variables(self, parent):
        """绑定变量到已存在的输入框"""
        # 这个方法在create_input_row之后调用，变量已经创建
        pass
    
    def browse_audio(self):
        """选择音频文件"""
        file_path = filedialog.askopenfilename(
            title="选择音频文件",
            filetypes=[("WAV files", "*.wav"), ("MP3 files", "*.mp3"), ("All files", "*.*")]
        )
        if file_path:
            self.audio_path_var.set(file_path)
            self.status_var.set(f"已选择音频: {os.path.basename(file_path)}")
    
    def clear_audio(self):
        """清除音频"""
        self.audio_path_var.set("")
        self.status_var.set("已清除音频文件")
    
    def load_example(self, name, age, gender, education):
        """加载示例"""
        self.name_var.set(name)
        self.age_var.set(age)
        self.gender_var.set(gender)
        self.education_var.set(education)
        self.status_var.set(f"已加载示例: {name}")
    
    def clear_all(self):
        """清空所有"""
        self.name_var.set("")
        self.age_var.set("")
        self.education_var.set("")
        self.audio_path_var.set("")
        self.result_text.delete(1.0, tk.END)
        self.status_var.set("已清空所有输入")
    
    def validate_input(self):
        """验证输入"""
        if not self.name_var.get().strip():
            messagebox.showerror("输入错误", "请输入患者姓名")
            return False
        
        try:
            age = int(self.age_var.get())
            if age < 0 or age > 120:
                raise ValueError()
        except:
            messagebox.showerror("输入错误", "请输入有效年龄（0-120）")
            return False
        
        try:
            education = int(self.education_var.get())
            if education < 0 or education > 30:
                raise ValueError()
        except:
            messagebox.showerror("输入错误", "请输入有效教育年限（0-30）")
            return False
        
        return True
    
    def start_analysis(self):
        """开始分析"""
        if not self.validate_input():
            return
        
        self.analyze_btn.config(state='disabled')
        self.progress.start()
        self.status_var.set("正在进行AI分析，请稍候...")
        
        thread = threading.Thread(target=self.run_analysis)
        thread.daemon = True
        thread.start()
    
    def run_analysis(self):
        """运行分析"""
        try:
            name = self.name_var.get().strip()
            age = int(self.age_var.get())
            gender = self.gender_var.get()
            education = int(self.education_var.get())
            audio_file = self.audio_path_var.get() if self.audio_path_var.get() else None
            
            self.root.after(0, lambda: self.status_var.set("正在加载AI模型..."))
            
            from easy_predict import detailed_predict
            
            self.root.after(0, lambda: self.status_var.set("AI分析中..."))
            
            result = detailed_predict(age=age, gender=gender, education_years=education, audio_file=audio_file)
            
            self.root.after(0, lambda: self.display_result(result, name, age, gender, education, audio_file))
            
        except Exception as e:
            self.root.after(0, lambda: self.display_error(str(e)))
    
    def display_result(self, result, name, age, gender, education, audio_file):
        """显示结果"""
        self.progress.stop()
        self.analyze_btn.config(state='normal')
        
        self.result_text.delete(1.0, tk.END)
        
        # 格式化结果
        result_text = f"""
🔍 AI检测报告

👤 患者信息:
   姓名: {name}
   年龄: {age}岁
   性别: {'男性' if gender == 'M' else '女性'}
   教育: {education}年
   语音: {'已分析' if audio_file and result.get('has_audio') else '未使用'}

🎯 检测结果:
   预测: {result['predicted_class']}
   置信度: {result['confidence']:.1%}

📊 详细概率:
   正常: {result['probabilities']['Normal']:.3f} ({result['probabilities']['Normal']*100:.1f}%)
   MCI: {result['probabilities']['MCI']:.3f} ({result['probabilities']['MCI']*100:.1f}%)
   痴呆: {result['probabilities']['Dementia']:.3f} ({result['probabilities']['Dementia']*100:.1f}%)

💡 建议:
"""
        
        if result['predicted_class'] == 'Normal':
            result_text += "   ✅ 认知功能正常，继续保持健康生活方式"
        elif result['predicted_class'] == 'MCI':
            result_text += "   ⚠️ 轻度认知障碍，建议咨询医生进一步评估"
        else:
            result_text += "   🔴 痴呆症风险，强烈建议尽快就医诊断"
        
        result_text += "\n\n📝 声明: 此结果仅供参考，不能替代专业医学诊断"
        
        self.result_text.insert(tk.END, result_text)
        
        # 设置结果颜色
        if result['predicted_class'] == 'Normal':
            self.result_text.tag_add("result", "7.0", "7.end")
            self.result_text.tag_config("result", foreground="green", font=('Arial', 12, 'bold'))
        elif result['predicted_class'] == 'MCI':
            self.result_text.tag_add("result", "7.0", "7.end")
            self.result_text.tag_config("result", foreground="orange", font=('Arial', 12, 'bold'))
        else:
            self.result_text.tag_add("result", "7.0", "7.end")
            self.result_text.tag_config("result", foreground="red", font=('Arial', 12, 'bold'))
        
        self.status_var.set("检测完成")
    
    def display_error(self, error_msg):
        """显示错误"""
        self.progress.stop()
        self.analyze_btn.config(state='normal')
        
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, f"❌ 检测失败: {error_msg}\n\n请检查输入信息或重试")
        
        self.status_var.set("检测失败")
        messagebox.showerror("检测错误", error_msg)

def main():
    """主函数"""
    root = tk.Tk()
    app = SimpleDementiaGUI(root)
    
    # 窗口居中
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")
    
    root.mainloop()

if __name__ == "__main__":
    main()
