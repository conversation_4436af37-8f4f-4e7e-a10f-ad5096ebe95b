#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
痴呆症检测工具 - GUI版本
高级图形界面，方便更多人使用
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import os
import warnings
warnings.filterwarnings('ignore')
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

class DementiaDetectorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("痴呆症检测工具 - AI智能分析")
        self.root.geometry("600x700")
        self.root.configure(bg='#f0f0f0')
        
        # 设置图标和样式
        self.setup_styles()
        
        # 创建界面
        self.create_widgets()
        
        # 模型状态
        self.model_loaded = False
        
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 自定义样式
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), background='#f0f0f0')
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'), background='#f0f0f0')
        style.configure('Info.TLabel', font=('Arial', 10), background='#f0f0f0')
        style.configure('Result.TLabel', font=('Arial', 12, 'bold'), background='#f0f0f0')
        
    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        title_frame = tk.Frame(self.root, bg='#f0f0f0')
        title_frame.pack(pady=20)
        
        title_label = ttk.Label(title_frame, text="🧠 AI痴呆症检测工具", style='Title.TLabel')
        title_label.pack()
        
        subtitle_label = ttk.Label(title_frame, text="基于多模态深度学习的认知功能评估", style='Info.TLabel')
        subtitle_label.pack(pady=5)
        
        # 输入区域
        input_frame = tk.LabelFrame(self.root, text="📝 输入信息", font=('Arial', 12, 'bold'), 
                                   bg='#f0f0f0', padx=20, pady=15)
        input_frame.pack(fill='x', padx=20, pady=10)
        
        # 年龄输入
        age_frame = tk.Frame(input_frame, bg='#f0f0f0')
        age_frame.pack(fill='x', pady=5)
        tk.Label(age_frame, text="年龄:", font=('Arial', 11), bg='#f0f0f0', width=12, anchor='w').pack(side='left')
        self.age_var = tk.StringVar()
        age_entry = tk.Entry(age_frame, textvariable=self.age_var, font=('Arial', 11), width=10)
        age_entry.pack(side='left', padx=5)
        tk.Label(age_frame, text="岁", font=('Arial', 11), bg='#f0f0f0').pack(side='left')
        
        # 性别选择
        gender_frame = tk.Frame(input_frame, bg='#f0f0f0')
        gender_frame.pack(fill='x', pady=5)
        tk.Label(gender_frame, text="性别:", font=('Arial', 11), bg='#f0f0f0', width=12, anchor='w').pack(side='left')
        self.gender_var = tk.StringVar(value="M")
        tk.Radiobutton(gender_frame, text="男性", variable=self.gender_var, value="M", 
                      font=('Arial', 11), bg='#f0f0f0').pack(side='left', padx=5)
        tk.Radiobutton(gender_frame, text="女性", variable=self.gender_var, value="F", 
                      font=('Arial', 11), bg='#f0f0f0').pack(side='left', padx=5)
        
        # 教育年限
        edu_frame = tk.Frame(input_frame, bg='#f0f0f0')
        edu_frame.pack(fill='x', pady=5)
        tk.Label(edu_frame, text="教育年限:", font=('Arial', 11), bg='#f0f0f0', width=12, anchor='w').pack(side='left')
        self.education_var = tk.StringVar()
        edu_entry = tk.Entry(edu_frame, textvariable=self.education_var, font=('Arial', 11), width=10)
        edu_entry.pack(side='left', padx=5)
        tk.Label(edu_frame, text="年", font=('Arial', 11), bg='#f0f0f0').pack(side='left')
        
        # 音频文件选择
        audio_frame = tk.LabelFrame(self.root, text="🎵 音频文件 (可选)", font=('Arial', 12, 'bold'), 
                                   bg='#f0f0f0', padx=20, pady=15)
        audio_frame.pack(fill='x', padx=20, pady=10)
        
        audio_select_frame = tk.Frame(audio_frame, bg='#f0f0f0')
        audio_select_frame.pack(fill='x', pady=5)
        
        self.audio_path_var = tk.StringVar()
        audio_entry = tk.Entry(audio_select_frame, textvariable=self.audio_path_var, 
                              font=('Arial', 10), state='readonly')
        audio_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))
        
        browse_btn = tk.Button(audio_select_frame, text="选择文件", command=self.browse_audio_file,
                              font=('Arial', 10), bg='#4CAF50', fg='white', padx=20)
        browse_btn.pack(side='right')
        
        clear_btn = tk.Button(audio_select_frame, text="清除", command=self.clear_audio_file,
                             font=('Arial', 10), bg='#f44336', fg='white', padx=15)
        clear_btn.pack(side='right', padx=(0, 5))
        
        # 预设示例
        example_frame = tk.LabelFrame(self.root, text="📋 快速示例", font=('Arial', 12, 'bold'), 
                                     bg='#f0f0f0', padx=20, pady=15)
        example_frame.pack(fill='x', padx=20, pady=10)
        
        examples = [
            ("90岁男性，20年教育", "90", "M", "20"),
            ("75岁女性，16年教育", "75", "F", "16"),
            ("65岁男性，12年教育", "65", "M", "12")
        ]
        
        for i, (desc, age, gender, edu) in enumerate(examples):
            btn = tk.Button(example_frame, text=desc, 
                           command=lambda a=age, g=gender, e=edu: self.load_example(a, g, e),
                           font=('Arial', 10), bg='#2196F3', fg='white', padx=10)
            btn.pack(side='left', padx=5, pady=5)
        
        # 分析按钮
        analyze_frame = tk.Frame(self.root, bg='#f0f0f0')
        analyze_frame.pack(pady=20)
        
        self.analyze_btn = tk.Button(analyze_frame, text="🔍 开始AI分析", command=self.start_analysis,
                                    font=('Arial', 14, 'bold'), bg='#FF9800', fg='white', 
                                    padx=40, pady=10)
        self.analyze_btn.pack()
        
        # 进度条
        self.progress = ttk.Progressbar(self.root, mode='indeterminate')
        self.progress.pack(fill='x', padx=20, pady=10)
        
        # 结果显示区域
        self.result_frame = tk.LabelFrame(self.root, text="📊 分析结果", font=('Arial', 12, 'bold'), 
                                         bg='#f0f0f0', padx=20, pady=15)
        self.result_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # 结果文本框
        self.result_text = tk.Text(self.result_frame, height=8, font=('Arial', 11), 
                                  bg='white', relief='sunken', bd=2)
        self.result_text.pack(fill='both', expand=True, pady=5)
        
        # 滚动条
        scrollbar = tk.Scrollbar(self.result_text)
        scrollbar.pack(side='right', fill='y')
        self.result_text.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.result_text.yview)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪 - 请输入信息并点击分析")
        status_bar = tk.Label(self.root, textvariable=self.status_var, relief='sunken', 
                             anchor='w', font=('Arial', 10), bg='#e0e0e0')
        status_bar.pack(fill='x', side='bottom')
        
    def browse_audio_file(self):
        """选择音频文件"""
        file_path = filedialog.askopenfilename(
            title="选择音频文件",
            filetypes=[("WAV files", "*.wav"), ("All files", "*.*")]
        )
        if file_path:
            self.audio_path_var.set(file_path)
            self.status_var.set(f"已选择音频文件: {os.path.basename(file_path)}")
    
    def clear_audio_file(self):
        """清除音频文件"""
        self.audio_path_var.set("")
        self.status_var.set("已清除音频文件")
    
    def load_example(self, age, gender, education):
        """加载示例数据"""
        self.age_var.set(age)
        self.gender_var.set(gender)
        self.education_var.set(education)
        self.status_var.set(f"已加载示例: {age}岁 {gender} {education}年教育")
    
    def validate_input(self):
        """验证输入数据"""
        try:
            age = int(self.age_var.get())
            if age < 0 or age > 120:
                raise ValueError("年龄应在0-120之间")
        except ValueError:
            messagebox.showerror("输入错误", "请输入有效的年龄（数字）")
            return False
        
        try:
            education = int(self.education_var.get())
            if education < 0 or education > 30:
                raise ValueError("教育年限应在0-30之间")
        except ValueError:
            messagebox.showerror("输入错误", "请输入有效的教育年限（数字）")
            return False
        
        return True

    def start_analysis(self):
        """开始分析"""
        if not self.validate_input():
            return

        # 禁用按钮，显示进度条
        self.analyze_btn.config(state='disabled')
        self.progress.start()
        self.status_var.set("正在分析中，请稍候...")

        # 在新线程中运行分析
        thread = threading.Thread(target=self.run_analysis)
        thread.daemon = True
        thread.start()

    def run_analysis(self):
        """运行分析（在后台线程）"""
        try:
            # 获取输入数据
            age = int(self.age_var.get())
            gender = self.gender_var.get()
            education = int(self.education_var.get())
            audio_file = self.audio_path_var.get() if self.audio_path_var.get() else None

            # 更新状态
            self.root.after(0, lambda: self.status_var.set("正在加载AI模型..."))

            # 导入并运行预测
            from easy_predict import detailed_predict

            self.root.after(0, lambda: self.status_var.set("正在进行AI分析..."))

            result = detailed_predict(
                age=age,
                gender=gender,
                education_years=education,
                audio_file=audio_file
            )

            # 在主线程中更新UI
            self.root.after(0, lambda: self.display_result(result, age, gender, education, audio_file))

        except Exception as e:
            error_msg = f"分析出错: {str(e)}"
            self.root.after(0, lambda: self.display_error(error_msg))

    def display_result(self, result, age, gender, education, audio_file):
        """显示分析结果"""
        # 停止进度条，启用按钮
        self.progress.stop()
        self.analyze_btn.config(state='normal')

        # 清空结果区域
        self.result_text.delete(1.0, tk.END)

        # 格式化结果
        result_text = f"""
🔍 AI分析完成！

📋 输入信息:
   年龄: {age}岁
   性别: {'男性' if gender == 'M' else '女性'}
   教育年限: {education}年
   音频文件: {'已使用' if audio_file and result.get('has_audio') else '未使用'}

🎯 预测结果:
   类别: {result['predicted_class']}
   置信度: {result['confidence']:.1%}

📊 详细概率:
   正常 (Normal): {result['probabilities']['Normal']:.3f}
   轻度认知障碍 (MCI): {result['probabilities']['MCI']:.3f}
   痴呆症 (Dementia): {result['probabilities']['Dementia']:.3f}

💡 结果解释:
"""

        if result['predicted_class'] == 'Normal':
            result_text += "   ✅ 认知功能正常\n   建议: 继续保持健康的生活方式"
        elif result['predicted_class'] == 'MCI':
            result_text += "   ⚠️ 轻度认知障碍\n   建议: 建议咨询医生进行进一步评估"
        elif result['predicted_class'] == 'Dementia':
            result_text += "   🔴 痴呆症风险\n   建议: 强烈建议尽快就医诊断"

        result_text += "\n\n📝 重要提醒:\n   此结果仅供参考，不能替代专业医学诊断。\n   如有疑虑，请咨询专业医生。"

        # 显示结果
        self.result_text.insert(tk.END, result_text)

        # 根据结果设置颜色
        if result['predicted_class'] == 'Normal':
            self.result_text.tag_add("normal", "6.0", "6.end")
            self.result_text.tag_config("normal", foreground="green", font=('Arial', 12, 'bold'))
        elif result['predicted_class'] == 'MCI':
            self.result_text.tag_add("mci", "6.0", "6.end")
            self.result_text.tag_config("mci", foreground="orange", font=('Arial', 12, 'bold'))
        else:
            self.result_text.tag_add("dementia", "6.0", "6.end")
            self.result_text.tag_config("dementia", foreground="red", font=('Arial', 12, 'bold'))

        self.status_var.set("分析完成")

    def display_error(self, error_msg):
        """显示错误信息"""
        self.progress.stop()
        self.analyze_btn.config(state='normal')

        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, f"❌ {error_msg}\n\n请检查输入信息或重试。")

        self.status_var.set("分析失败")
        messagebox.showerror("分析错误", error_msg)

def main():
    """主函数"""
    root = tk.Tk()
    app = DementiaDetectorGUI(root)

    # 设置窗口居中
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")

    root.mainloop()

if __name__ == "__main__":
    main()
