#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版痴呆症检测工具
支持格式: python final_predict.py 90 M 20 音频文件路径
"""

import sys
import os
import warnings
warnings.filterwarnings('ignore')
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

def show_usage():
    """显示使用说明"""
    print("=== 痴呆症检测工具 ===")
    print("\n使用格式:")
    print("python final_predict.py 年龄 性别 教育年限 [音频文件路径]")
    print("\n示例:")
    print("python final_predict.py 90 M 20")
    print("python final_predict.py 90 M 20 test_audio.wav")
    print("python final_predict.py 75 F 16 D:\\audio\\sample.wav")
    print("\n参数说明:")
    print("年龄: 数字 (如 90)")
    print("性别: M 或 F")
    print("教育年限: 数字 (如 20)")
    print("音频文件路径: 可选，.wav 文件完整路径")

def main():
    # 检查参数
    if len(sys.argv) < 4:
        show_usage()
        return
    
    try:
        # 解析参数
        age = int(sys.argv[1])
        gender = sys.argv[2].upper()
        education_years = int(sys.argv[3])
        audio_file = sys.argv[4] if len(sys.argv) > 4 else None
        
        # 验证参数
        if gender not in ['M', 'F']:
            print(f"错误: 性别必须是 M 或 F，您输入的是: {gender}")
            return
        
        # 显示输入信息
        print(f"输入: {age} {gender} {education_years}", end="")
        if audio_file:
            print(f" {audio_file}")
            if not os.path.exists(audio_file):
                print(f"警告: 音频文件不存在: {audio_file}")
                audio_file = None
        else:
            print()
        
        print("正在预测...")
        
        # 导入并预测
        from easy_predict import detailed_predict
        
        result = detailed_predict(
            age=age,
            gender=gender,
            education_years=education_years,
            audio_file=audio_file
        )
        
        # 输出结果
        print(f"\n=== 结果 ===")
        print(f"预测: {result['predicted_class']}")
        print(f"置信度: {result['confidence']:.1%}")
        
        if audio_file and result['has_audio']:
            print("音频特征: 已使用")
        else:
            print("音频特征: 未使用")
        
        # 简单解释
        if result['predicted_class'] == 'Normal':
            print("状态: 认知功能正常")
        elif result['predicted_class'] == 'MCI':
            print("状态: 轻度认知障碍")
        elif result['predicted_class'] == 'Dementia':
            print("状态: 痴呆症")
        
        print("\n详细概率:")
        for name, prob in result['probabilities'].items():
            print(f"  {name}: {prob:.3f}")
        
    except ValueError as e:
        print(f"参数错误: {e}")
        print("年龄和教育年限必须是数字")
    except Exception as e:
        print(f"预测错误: {e}")

if __name__ == "__main__":
    main()
