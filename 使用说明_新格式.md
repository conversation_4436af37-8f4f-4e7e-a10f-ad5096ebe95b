# 痴呆症检测模型 - 新格式使用说明

## 🎯 支持的输入格式

### 格式1: 不包含音频
```bash
python final_predict.py 年龄 性别 教育年限
```

### 格式2: 包含音频文件
```bash
python final_predict.py 年龄 性别 教育年限 音频文件路径
```

## 📋 具体使用示例

### 1. 基础预测（您的格式）
```bash
python final_predict.py 90 M 20
```
**输出:**
```
输入: 90 M 20
正在预测...
=== 结果 ===
预测: Normal
置信度: 100.0%
音频特征: 未使用
状态: 认知功能正常
```

### 2. 包含音频的预测
```bash
python final_predict.py 90 M 20 test_audio.wav
```
**输出:**
```
输入: 90 M 20 test_audio.wav
正在预测...
=== 结果 ===
预测: Normal
置信度: 100.0%
音频特征: 已使用
状态: 认知功能正常
```

### 3. 更多示例
```bash
# 75岁女性，16年教育
python final_predict.py 75 F 16

# 65岁男性，12年教育，包含音频
python final_predict.py 65 M 12 D:\audio\sample.wav

# 80岁女性，8年教育
python final_predict.py 80 F 8
```

## 📁 可用的脚本文件

### 🌟 **推荐使用: `final_predict.py`**
- **最简单的命令行工具**
- 支持您要求的格式: `90 M 20 音频文件路径`
- 输出简洁清晰

### 🔧 其他可用工具

1. **`audio_predict.py`** - 专门的音频分析工具
   ```bash
   python audio_predict.py 90 M 20 test_audio.wav
   ```

2. **`predict_cli.py`** - 通用命令行工具
   ```bash
   python predict_cli.py 90 M 20 test_audio.wav
   ```

3. **`quick_predict.py`** - 修改文件内变量的方式
   - 打开文件修改 AGE, GENDER, EDUCATION 变量
   - 运行 `python quick_predict.py`

4. **`easy_predict.py`** - Python 代码调用
   ```python
   from easy_predict import predict_dementia
   result = predict_dementia(90, 'M', 20, 'audio.wav')
   ```

## 🎵 音频文件要求

- **格式**: .wav 文件
- **路径**: 完整的文件路径
- **示例路径**:
  - Windows: `D:\audio\sample.wav`
  - 相对路径: `test_audio.wav`
  - 网络路径: `\\server\audio\file.wav`

## 📊 输出结果说明

### 预测类别
- **Normal**: 认知功能正常
- **MCI**: 轻度认知障碍
- **Dementia**: 痴呆症

### 置信度
- 显示为百分比 (如 100.0%)
- 数值越高表示模型越确信

### 音频特征状态
- **已使用**: 成功提取并使用了音频特征
- **未使用**: 没有音频文件或提取失败

## 🚀 快速开始

1. **测试基础功能**:
   ```bash
   python final_predict.py 90 M 20
   ```

2. **创建测试音频**:
   ```bash
   python create_test_audio.py
   ```

3. **测试音频功能**:
   ```bash
   python final_predict.py 90 M 20 test_audio.wav
   ```

## ⚠️ 常见问题

### Q: 显示 "参数错误"
A: 检查输入格式，确保年龄和教育年限是数字

### Q: 显示 "音频文件不存在"
A: 检查文件路径是否正确，使用完整路径

### Q: 音频特征提取失败
A: 确保音频文件是 .wav 格式，文件没有损坏

### Q: 所有预测结果都是 Normal
A: 这可能是模型特性，建议提供音频文件获得更准确的结果

## 📝 注意事项

1. **首次运行**: 需要加载模型，可能需要几秒钟
2. **音频文件**: 推荐使用 .wav 格式，22050Hz 采样率
3. **结果解释**: 仅供参考，不能替代专业医学诊断
4. **路径格式**: Windows 系统使用反斜杠 `\` 或双反斜杠 `\\`

## 🎯 您的专用格式

**完全支持您要求的格式:**
```bash
python final_predict.py 90 M 20 音频文件路径
```

**示例:**
```bash
python final_predict.py 90 M 20 D:\audio\sample.wav
python final_predict.py 75 F 16 C:\Users\<USER>