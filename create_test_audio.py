#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试音频文件
"""

import numpy as np
import os
import warnings
warnings.filterwarnings('ignore')

def create_test_audio():
    """创建一个简单的测试音频文件"""
    try:
        import soundfile as sf
        
        # 生成测试音频数据 (5秒，22050Hz)
        duration = 5  # 秒
        sample_rate = 22050
        t = np.linspace(0, duration, duration * sample_rate, False)
        
        # 生成混合频率的音频信号 (模拟语音)
        frequency1 = 440  # A4音符
        frequency2 = 880  # A5音符
        audio_data = (np.sin(frequency1 * 2 * np.pi * t) * 0.3 + 
                     np.sin(frequency2 * 2 * np.pi * t) * 0.2 +
                     np.random.normal(0, 0.1, len(t)))  # 添加噪声
        
        # 确保音频数据在合理范围内
        audio_data = np.clip(audio_data, -1.0, 1.0)
        
        # 保存音频文件
        output_path = "test_audio.wav"
        sf.write(output_path, audio_data, sample_rate)
        
        print(f"✅ 测试音频文件已创建: {output_path}")
        print(f"   时长: {duration}秒")
        print(f"   采样率: {sample_rate}Hz")
        print(f"   文件大小: {os.path.getsize(output_path) / 1024:.1f} KB")
        
        return output_path
        
    except ImportError:
        print("❌ 需要安装 soundfile: pip install soundfile")
        return None
    except Exception as e:
        print(f"❌ 创建音频文件失败: {e}")
        return None

def main():
    print("=== 创建测试音频文件 ===")
    
    audio_file = create_test_audio()
    
    if audio_file:
        print(f"\n🎵 现在您可以使用以下命令测试:")
        print(f"python audio_predict.py 90 M 20 {audio_file}")
        print(f"python predict_cli.py 90 M 20 {audio_file}")
        
        # 自动测试
        print(f"\n🔄 自动测试中...")
        try:
            import subprocess
            result = subprocess.run([
                "python", "audio_predict.py", "90", "M", "20", audio_file
            ], capture_output=True, text=True, cwd=".")
            
            if result.returncode == 0:
                print("✅ 自动测试成功!")
                print(result.stdout)
            else:
                print("❌ 自动测试失败:")
                print(result.stderr)
                
        except Exception as e:
            print(f"自动测试出错: {e}")

if __name__ == "__main__":
    main()
