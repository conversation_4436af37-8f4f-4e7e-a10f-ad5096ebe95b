import pandas as pd
import numpy as np
import json
import os
import time
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import Dense, Dropout, BatchNormalization, Input, Concatenate
from tensorflow.keras.optimizers import <PERSON>
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.utils import to_categorical
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, accuracy_score, recall_score

print("Starting TensorFlow Dementia Model")
print(f"TensorFlow version: {tf.__version__}")
print(f"Start time: {time.strftime('%Y-%m-%d %H:%M:%S')}")

# 配置
TRAIN_DATA_PATH = "train_set_scaled.csv"
VALIDATION_DATA_PATH = "validation_set_scaled.csv"
OUTPUT_MODEL_PATH = "tf_dementia_model.h5"
OUTPUT_REPORT_PATH = "tf_training_report.json"
AUTODL_OUTPUT_DIR = "/root/autodl-tmp/models"
AUTODL_BACKUP = True

# 创建AutoDL目录
if AUTODL_BACKUP:
    os.makedirs(AUTODL_OUTPUT_DIR, exist_ok=True)

# 数据加载和预处理
print("Loading data...")
train_data = pd.read_csv(TRAIN_DATA_PATH)
val_data = pd.read_csv(VALIDATION_DATA_PATH)
print(f"Train data: {train_data.shape}")
print(f"Validation data: {val_data.shape}")

# 确定目标列
target_col = None
for col in ['diagnosis_encoded', 'diagnosis']:
    if col in train_data.columns:
        target_col = col
        break

if target_col is None:
    raise ValueError("No diagnosis column found")

print(f"Target column: {target_col}")

# 特征分组 - 根据领域知识将特征分组
feature_groups = {
    'demographic': ['age', 'gender', 'education_years'],
    'acoustic': ['f0_mean', 'f0_std', 'f0_range', 'jitter_percent', 'shimmer_percent', 
                'hnr_db', 'spectral_centroid', 'spectral_bandwidth', 'spectral_rolloff', 
                'zero_crossing_rate'],
    'mfcc': [col for col in train_data.columns if 'mfcc' in col],
    'linguistic': ['speech_rate_wpm', 'pause_frequency', 'pause_duration_mean', 
                  'total_words', 'unique_words', 'type_token_ratio', 
                  'information_units', 'efficiency_ratio', 'coherence_score'],
    'errors': ['filled_pauses', 'repetitions', 'semantic_errors']
}

# 验证特征存在
available_features = {}
for group, features in feature_groups.items():
    available = [f for f in features if f in train_data.columns]
    if available:
        available_features[group] = available
        print(f"{group} features: {len(available)}")

# 准备数据
X_groups = {}
X_val_groups = {}

for group, features in available_features.items():
    # 标准化每组特征
    scaler = StandardScaler()
    X_groups[group] = scaler.fit_transform(train_data[features])
    X_val_groups[group] = scaler.transform(val_data[features])

# 标签编码
label_encoder = LabelEncoder()
if target_col == 'diagnosis':
    y_train = label_encoder.fit_transform(train_data[target_col])
    y_val = label_encoder.transform(val_data[target_col])
else:
    y_train = train_data[target_col].values
    y_val = val_data[target_col].values

# 分析类别分布
class_counts = np.bincount(y_train)
n_classes = len(class_counts)
print(f"Number of classes: {n_classes}")
print("Class distribution:")
for i, count in enumerate(class_counts):
    print(f"  Class {i}: {count} ({count/len(y_train):.1%})")

# 转换为one-hot编码
y_train_cat = to_categorical(y_train, num_classes=n_classes)
y_val_cat = to_categorical(y_val, num_classes=n_classes)

# 计算类别权重
class_weights = {i: len(y_train) / (n_classes * count) for i, count in enumerate(class_counts)}
print(f"Class weights: {class_weights}")

# 构建多输入模型
def build_multimodal_model(input_shapes, n_classes):
    inputs = []
    feature_networks = []
    
    # 为每组特征创建子网络
    for group, shape in input_shapes.items():
        inp = Input(shape=(shape,), name=f"{group}_input")
        inputs.append(inp)
        
        # 为每组特征创建特定的子网络
        if group == 'demographic':
            # 简单网络用于人口统计学特征
            x = Dense(8, activation='relu')(inp)
            x = BatchNormalization()(x)
            feature_networks.append(x)
        
        elif group == 'acoustic':
            # 声学特征的深层网络
            x = Dense(32, activation='relu')(inp)
            x = BatchNormalization()(x)
            x = Dropout(0.3)(x)
            x = Dense(16, activation='relu')(x)
            x = BatchNormalization()(x)
            feature_networks.append(x)
        
        elif group == 'mfcc':
            # MFCC特征的深层网络
            x = Dense(64, activation='relu')(inp)
            x = BatchNormalization()(x)
            x = Dropout(0.3)(x)
            x = Dense(32, activation='relu')(x)
            x = BatchNormalization()(x)
            feature_networks.append(x)
        
        elif group == 'linguistic':
            # 语言特征的网络
            x = Dense(32, activation='relu')(inp)
            x = BatchNormalization()(x)
            x = Dropout(0.3)(x)
            x = Dense(16, activation='relu')(x)
            x = BatchNormalization()(x)
            feature_networks.append(x)
        
        else:  # errors or other groups
            # 其他特征的简单网络
            x = Dense(16, activation='relu')(inp)
            x = BatchNormalization()(x)
            feature_networks.append(x)
    
    # 合并所有特征网络
    if len(feature_networks) > 1:
        combined = Concatenate()(feature_networks)
    else:
        combined = feature_networks[0]
    
    # 共享层
    x = Dense(128, activation='relu')(combined)
    x = BatchNormalization()(x)
    x = Dropout(0.5)(x)
    x = Dense(64, activation='relu')(x)
    x = BatchNormalization()(x)
    x = Dropout(0.4)(x)
    x = Dense(32, activation='relu')(x)
    x = BatchNormalization()(x)
    
    # 输出层
    output = Dense(n_classes, activation='softmax')(x)
    
    model = Model(inputs=inputs, outputs=output)
    return model

# 构建和训练模型
print("Building TensorFlow model...")

# 准备输入形状
input_shapes = {group: X.shape[1] for group, X in X_groups.items()}

# 构建模型
model = build_multimodal_model(input_shapes, n_classes)

# 编译模型
model.compile(
    optimizer=Adam(learning_rate=0.001),
    loss='categorical_crossentropy',
    metrics=['accuracy']
)

# 打印模型摘要
model.summary()

# 回调函数
callbacks = [
    EarlyStopping(monitor='val_loss', patience=20, restore_best_weights=True),
    ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=5, min_lr=0.00001)
]

# 训练模型
print("Training model...")
history = model.fit(
    [X_groups[group] for group in X_groups.keys()],
    y_train_cat,
    validation_data=([X_val_groups[group] for group in X_groups.keys()], y_val_cat),
    epochs=100,
    batch_size=32,
    class_weight=class_weights,
    callbacks=callbacks,
    verbose=1
)

# 评估模型
print("Evaluating model...")
y_pred_prob = model.predict([X_val_groups[group] for group in X_groups.keys()])
y_pred = np.argmax(y_pred_prob, axis=1)

accuracy = accuracy_score(y_val, y_pred)
recalls = recall_score(y_val, y_pred, average=None)
report = classification_report(y_val, y_pred, output_dict=True)

print(f"Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
print("Class recalls:")
for i, recall in enumerate(recalls):
    print(f"  Class {i}: {recall:.4f} ({recall*100:.2f}%)")

# 特别关注痴呆症类别的召回率 (假设类别0是痴呆症)
dementia_recall = recalls[0]
print(f"Dementia recall: {dementia_recall:.4f} ({dementia_recall*100:.2f}%)")

# 医疗安全评级
if dementia_recall >= 0.9:
    safety_level = "Excellent"
elif dementia_recall >= 0.75:
    safety_level = "Good"
elif dementia_recall >= 0.6:
    safety_level = "Fair"
else:
    safety_level = "Poor"

print(f"Medical safety: {safety_level}")

# 保存模型和结果
print("Saving model and results...")

# 保存TensorFlow模型
model.save(OUTPUT_MODEL_PATH)
print(f"TensorFlow model saved: {OUTPUT_MODEL_PATH}")

# 保存到AutoDL
if AUTODL_BACKUP:
    try:
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        autodl_tf_path = os.path.join(AUTODL_OUTPUT_DIR, f"tf_dementia_model_{timestamp}.h5")
        model.save(autodl_tf_path)
        print(f"Model backed up to AutoDL: {autodl_tf_path}")
        
        # 保存模型元数据
        metadata = {
            'model_type': 'tensorflow',
            'feature_groups': {group: list(features) for group, features in available_features.items()},
            'label_encoder': {i: str(label) for i, label in enumerate(label_encoder.classes_)},
            'training_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'accuracy': float(accuracy),
            'dementia_recall': float(dementia_recall),
            'safety_level': safety_level
        }
        
        metadata_path = os.path.join(AUTODL_OUTPUT_DIR, f"tf_model_metadata_{timestamp}.json")
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        print(f"Model metadata saved: {metadata_path}")
    except Exception as e:
        print(f"AutoDL backup failed: {e}")

# 准备训练报告
training_report = {
    'model_type': 'tensorflow_multimodal',
    'training_time': time.strftime('%Y-%m-%d %H:%M:%S'),
    'tensorflow_version': tf.__version__,
    'data_info': {
        'train_samples': len(y_train),
        'validation_samples': len(y_val),
        'feature_groups': {group: len(features) for group, features in available_features.items()},
        'total_features': sum(len(features) for features in available_features.values()),
        'classes': n_classes,
        'class_distribution': {str(i): int(count) for i, count in enumerate(class_counts)}
    },
    'model_info': {
        'architecture': 'multimodal_neural_network',
        'input_branches': len(X_groups),
        'class_weights': {str(k): float(v) for k, v in class_weights.items()}
    },
    'training_results': {
        'accuracy': float(accuracy),
        'class_recalls': {str(i): float(r) for i, r in enumerate(recalls)},
        'dementia_recall': float(dementia_recall),
        'safety_level': safety_level,
        'clinical_ready': dementia_recall >= 0.8
    },
    'classification_report': {
        str(k): {kk: float(vv) for kk, vv in v.items() if isinstance(vv, (int, float))}
        for k, v in report.items() if k not in ['accuracy', 'macro avg', 'weighted avg']
    }
}

# 保存训练报告
try:
    with open(OUTPUT_REPORT_PATH, 'w') as f:
        json.dump(training_report, f, indent=2)
    print(f"Training report saved: {OUTPUT_REPORT_PATH}")
    
    if AUTODL_BACKUP:
        autodl_report_path = os.path.join(AUTODL_OUTPUT_DIR, f"tf_training_report_{timestamp}.json")
        with open(autodl_report_path, 'w') as f:
            json.dump(training_report, f, indent=2)
        print(f"Report backed up to AutoDL: {autodl_report_path}")
except Exception as e:
    print(f"Report save failed: {e}")

# 最终总结
print("\nTraining completed!")
print(f"Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
print(f"Dementia recall: {dementia_recall:.4f} ({dementia_recall*100:.2f}%)")
print(f"Medical safety: {safety_level}")
print(f"Model file: {OUTPUT_MODEL_PATH}")
print(f"Report file: {OUTPUT_REPORT_PATH}")

if AUTODL_BACKUP:
    print(f"AutoDL backup: {AUTODL_OUTPUT_DIR}")

print(f"End time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
print("TensorFlow Dementia Model completed!")