#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接使用示例 - 90,M,12 格式
"""

import os
import warnings
warnings.filterwarnings('ignore')
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

from easy_predict import predict_dementia

# 直接使用您的格式: 90,M,12
print("=== 直接使用示例 ===")

# 方法1: 直接调用
result = predict_dementia(90, 'M', 12)
print(f"输入: 90,M,12")
print(f"结果: {result}")

print("\n" + "-"*40)

# 方法2: 更多例子
examples = [
    (90, 'M', 12),  # 您的例子
    (75, 'F', 16),  # 75岁女性，16年教育
    (65, 'M', 10),  # 65岁男性，10年教育
    (80, 'F', 8),   # 80岁女性，8年教育
]

print("更多输入示例:")
for age, gender, edu in examples:
    result = predict_dementia(age, gender, edu)
    print(f"{age},{gender},{edu} -> {result}")

print("\n=== 一行代码调用 ===")
print("predict_dementia(90, 'M', 12)")
print("predict_dementia(75, 'F', 16)")
print("predict_dementia(65, 'M', 10)")
