# 🧠 AI痴呆症检测工具 - GUI版本使用说明

## 🎯 GUI版本优势

### ✅ **为什么选择GUI版本？**
- **用户友好**: 图形界面，无需命令行操作
- **操作简单**: 点击按钮即可完成检测
- **功能丰富**: 支持音频上传、历史记录、报告导出
- **适合更多人**: 医生、护士、家属都能轻松使用
- **专业外观**: 医疗级界面设计

## 📁 可用的GUI版本

### 🌟 **推荐: `simple_gui.py` - 简化版**
- **最易使用**: 界面简洁，功能完整
- **快速上手**: 适合初次使用者
- **稳定可靠**: 经过优化的核心功能

### 🚀 **高级版: `advanced_gui.py` - 专业版**
- **功能丰富**: 历史记录、统计分析、批量检测
- **专业界面**: 多标签页设计
- **适合机构**: 医院、诊所等专业场所

### 💡 **基础版: `dementia_gui.py` - 标准版**
- **功能均衡**: 基础功能 + 部分高级特性
- **中等复杂度**: 适合有一定经验的用户

## 🚀 快速开始

### 1. 启动GUI
```bash
# 推荐使用简化版
python simple_gui.py

# 或使用高级版
python advanced_gui.py

# 或使用标准版
python dementia_gui.py
```

### 2. 界面介绍

#### 📝 **患者信息区域**
- **姓名**: 输入患者姓名
- **年龄**: 输入年龄（0-120岁）
- **性别**: 选择男性或女性
- **教育年限**: 输入受教育年数（0-30年）
- **快速示例**: 点击示例按钮快速填充数据

#### 🎵 **语音文件区域**
- **选择文件**: 点击"📁 选择"按钮上传音频文件
- **支持格式**: .wav, .mp3 等音频格式
- **清除文件**: 点击"❌ 清除"移除已选择的文件

#### 🔍 **检测控制**
- **开始AI检测**: 点击蓝色按钮开始分析
- **清空**: 清除所有输入内容
- **进度条**: 显示检测进度

#### 📊 **结果显示**
- **详细报告**: 显示完整的检测结果
- **概率分布**: 各类别的详细概率
- **专业建议**: 基于结果的医疗建议

## 📋 使用步骤

### 步骤1: 输入基本信息
1. 在"姓名"框输入患者姓名
2. 在"年龄"框输入年龄数字
3. 选择性别（男/女）
4. 输入教育年限

### 步骤2: 上传音频（可选）
1. 点击"📁 选择"按钮
2. 选择音频文件（推荐.wav格式）
3. 确认文件路径显示正确

### 步骤3: 开始检测
1. 点击"🔍 开始AI检测"按钮
2. 等待进度条完成（通常需要10-30秒）
3. 查看检测结果

### 步骤4: 查看结果
1. 在结果区域查看详细报告
2. 注意预测类别和置信度
3. 阅读专业建议

## 🎯 功能特色

### ✨ **智能特性**
- **多模态分析**: 结合人口统计学数据和语音特征
- **实时检测**: 快速AI分析，秒级出结果
- **置信度评估**: 显示预测的可信程度
- **专业建议**: 基于结果提供医疗建议

### 🛡️ **安全特性**
- **数据保护**: 本地处理，不上传个人信息
- **错误处理**: 完善的输入验证和错误提示
- **稳定运行**: 多线程处理，界面不卡顿

### 📈 **高级功能**（高级版）
- **历史记录**: 自动保存检测历史
- **批量检测**: 支持多个患者批量分析
- **报告导出**: 导出PDF或文本报告
- **统计分析**: 数据统计和趋势分析

## 💡 使用技巧

### 🎯 **提高准确性**
1. **完整信息**: 尽量填写所有基本信息
2. **音频质量**: 使用清晰的语音录音
3. **多次检测**: 可以多次检测对比结果

### ⚡ **提高效率**
1. **快速示例**: 使用示例按钮快速填充
2. **批量处理**: 使用高级版的批量功能
3. **保存结果**: 及时保存重要的检测结果

### 🔧 **故障排除**
1. **模型加载慢**: 首次使用需要加载模型，请耐心等待
2. **音频识别失败**: 检查音频格式，推荐使用.wav
3. **界面卡顿**: 检测过程中请勿频繁操作

## 📊 结果解读

### 🎯 **预测类别**
- **Normal**: 认知功能正常
- **MCI**: 轻度认知障碍
- **Dementia**: 痴呆症

### 📈 **置信度**
- **90%以上**: 高置信度，结果较可靠
- **70-90%**: 中等置信度，建议结合其他检查
- **70%以下**: 低置信度，建议重新检测

### 💡 **专业建议**
- **正常**: 继续保持健康生活方式
- **MCI**: 建议咨询医生进一步评估
- **痴呆**: 强烈建议尽快就医诊断

## ⚠️ 重要提醒

### 🏥 **医疗声明**
- 此工具仅供**辅助参考**，不能替代专业医学诊断
- 如有疑虑，请及时咨询专业医生
- 建议结合临床检查和医生意见

### 🔒 **隐私保护**
- 所有数据在本地处理，不会上传到服务器
- 历史记录保存在本地文件中
- 可随时清除历史数据

### 📞 **技术支持**
- 遇到问题可查看错误提示
- 确保所有依赖包已正确安装
- 建议在稳定的网络环境下使用

## 🎉 总结

GUI版本让AI痴呆症检测变得更加**简单易用**，无论是医疗专业人士还是普通用户都能轻松上手。通过图形界面，您可以：

✅ **快速检测**: 几分钟完成专业级认知评估  
✅ **直观结果**: 清晰的报告和专业建议  
✅ **安全可靠**: 本地处理，保护隐私  
✅ **持续改进**: 基于深度学习的智能分析  

**立即体验**: `python simple_gui.py` 🚀
