#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
命令行痴呆症检测工具
使用方法: python predict_cli.py 年龄 性别 教育年限 [音频文件]
示例: python predict_cli.py 90 M 20
"""

import sys
import os
import warnings
warnings.filterwarnings('ignore')
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

def main():
    # 检查参数数量
    if len(sys.argv) < 4:
        print("使用方法:")
        print("python predict_cli.py 年龄 性别 教育年限 [音频文件路径]")
        print("\n示例:")
        print("python predict_cli.py 90 M 20")
        print("python predict_cli.py 90 M 20 D:\\audio\\sample.wav")
        print("python predict_cli.py 75 F 16 C:\\Users\\<USER>\n参数说明:")
        print("年龄: 数字 (如 90)")
        print("性别: M 或 F")
        print("教育年限: 数字 (如 20)")
        print("音频文件路径: 可选，完整的音频文件路径")
        print("\n支持的音频格式: .wav")
        return
    
    try:
        # 解析命令行参数
        age = int(sys.argv[1])
        gender = sys.argv[2].upper()
        education_years = int(sys.argv[3])
        audio_file = sys.argv[4] if len(sys.argv) > 4 else None
        
        # 验证性别参数
        if gender not in ['M', 'F']:
            print(f"错误: 性别必须是 M 或 F，您输入的是: {gender}")
            return
        
        print(f"正在分析: {age}岁 {gender} 教育{education_years}年")
        if audio_file:
            if os.path.exists(audio_file):
                print(f"音频文件: {audio_file} ✅")
            else:
                print(f"音频文件: {audio_file} ❌ (文件不存在)")

        # 导入预测模块
        from easy_predict import detailed_predict
        
        # 进行预测
        result = detailed_predict(
            age=age, 
            gender=gender, 
            education_years=education_years, 
            audio_file=audio_file
        )
        
        # 输出结果
        print("\n=== 预测结果 ===")
        print(f"预测类别: {result['predicted_class']}")
        print(f"置信度: {result['confidence']:.1%}")
        
        print("\n详细概率:")
        for class_name, prob in result['probabilities'].items():
            print(f"  {class_name}: {prob:.6f}")
        
        print(f"\n使用音频特征: {'是' if result['has_audio'] else '否'}")
        
        # 简单解释
        print("\n=== 结果解释 ===")
        if result['predicted_class'] == 'Normal':
            print("✅ 认知功能正常")
        elif result['predicted_class'] == 'MCI':
            print("⚠️  轻度认知障碍")
        elif result['predicted_class'] == 'Dementia':
            print("🔴 痴呆症")
        
    except ValueError as e:
        print(f"参数错误: {e}")
        print("年龄和教育年限必须是数字")
    except Exception as e:
        print(f"预测错误: {e}")

if __name__ == "__main__":
    main()
