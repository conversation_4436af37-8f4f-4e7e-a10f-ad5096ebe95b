#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速预测脚本 - 直接修改这里的数值即可
"""

import os
import warnings
warnings.filterwarnings('ignore')
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

# ========================================
# 在这里修改您要预测的数据
# ========================================

# 您的输入数据 (直接修改这些数值)
AGE = 90           # 年龄
GENDER = 'M'       # 性别: 'M' 或 'F'
EDUCATION = 20     # 教育年限
AUDIO_FILE = None  # 音频文件路径，如果没有就保持 None

# ========================================

def main():
    print("=== 痴呆症检测快速预测 ===")
    print(f"输入数据: {AGE}, {GENDER}, {EDUCATION}")
    
    try:
        from easy_predict import predict_dementia, detailed_predict
        
        # 快速预测
        quick_result = predict_dementia(AGE, GENDER, EDUCATION, AUDIO_FILE)
        print(f"\n快速结果: {quick_result}")
        
        # 详细预测
        detailed_result = detailed_predict(AGE, GENDER, EDUCATION, AUDIO_FILE)
        
        print(f"\n=== 详细结果 ===")
        print(f"预测类别: {detailed_result['predicted_class']}")
        print(f"置信度: {detailed_result['confidence']:.1%}")
        
        print(f"\n各类别概率:")
        for class_name, prob in detailed_result['probabilities'].items():
            bar = "█" * int(prob * 20)  # 简单的进度条
            print(f"  {class_name:8}: {prob:.6f} {bar}")
        
        # 结果解释
        result_map = {
            'Normal': '✅ 认知功能正常',
            'MCI': '⚠️  轻度认知障碍', 
            'Dementia': '🔴 痴呆症'
        }
        
        print(f"\n结果解释: {result_map.get(detailed_result['predicted_class'], '未知')}")
        
    except Exception as e:
        print(f"预测出错: {e}")

if __name__ == "__main__":
    main()
    
    print(f"\n=== 使用说明 ===")
    print(f"要预测其他数据，请修改文件开头的变量:")
    print(f"AGE = {AGE}        # 修改年龄")
    print(f"GENDER = '{GENDER}'     # 修改性别 ('M' 或 'F')")
    print(f"EDUCATION = {EDUCATION}    # 修改教育年限")
    print(f"然后重新运行: python quick_predict.py")
