# 保存模型和结果
print("Saving model and results...")

# 保存TensorFlow模型
model.save(OUTPUT_MODEL_PATH)
print(f"TensorFlow model saved: {OUTPUT_MODEL_PATH}")

# 保存到AutoDL
if AUTODL_BACKUP:
    try:
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        autodl_tf_path = os.path.join(AUTODL_OUTPUT_DIR, f"tf_dementia_model_{timestamp}.h5")
        model.save(autodl_tf_path)
        print(f"Model backed up to AutoDL: {autodl_tf_path}")
        
        # 保存模型元数据
        metadata = {
            'model_type': 'tensorflow',
            'feature_groups': {group: list(features) for group, features in available_features.items()},
            'label_encoder': {i: str(label) for i, label in enumerate(label_encoder.classes_)},
            'training_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'accuracy': float(accuracy),
            'dementia_recall': float(dementia_recall),
            'safety_level': safety_level
        }
        
        metadata_path = os.path.join(AUTODL_OUTPUT_DIR, f"tf_model_metadata_{timestamp}.json")
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        print(f"Model metadata saved: {metadata_path}")
    except Exception as e:
        print(f"AutoDL backup failed: {e}")

# 准备训练报告
training_report = {
    'model_type': 'tensorflow_multimodal',
    'training_time': time.strftime('%Y-%m-%d %H:%M:%S'),
    'tensorflow_version': tf.__version__,
    'data_info': {
        'train_samples': len(y_train),
        'validation_samples': len(y_val),
        'feature_groups': {group: len(features) for group, features in available_features.items()},
        'total_features': sum(len(features) for features in available_features.values()),
        'classes': n_classes,
        'class_distribution': {str(i): int(count) for i, count in enumerate(class_counts)}
    },
    'model_info': {
        'architecture': 'multimodal_neural_network',
        'input_branches': len(X_groups),
        'class_weights': {str(k): float(v) for k, v in class_weights.items()}
    },
    'training_results': {
        'accuracy': float(accuracy),
        'class_recalls': {str(i): float(r) for i, r in enumerate(recalls)},
        'dementia_recall': float(dementia_recall),
        'safety_level': safety_level,
        'clinical_ready': dementia_recall >= 0.8
    },
    'classification_report': {
        str(k): {kk: float(vv) for kk, vv in v.items() if isinstance(vv, (int, float))}
        for k, v in report.items() if k not in ['accuracy', 'macro avg', 'weighted avg']
    }
}

# 保存训练报告
try:
    with open(OUTPUT_REPORT_PATH, 'w') as f:
        json.dump(training_report, f, indent=2)
    print(f"Training report saved: {OUTPUT_REPORT_PATH}")
    
    if AUTODL_BACKUP:
        autodl_report_path = os.path.join(AUTODL_OUTPUT_DIR, f"tf_training_report_{timestamp}.json")
        with open(autodl_report_path, 'w') as f:
            json.dump(training_report, f, indent=2)
        print(f"Report backed up to AutoDL: {autodl_report_path}")
except Exception as e:
    print(f"Report save failed: {e}")

# 最终总结
print("\nTraining completed!")
print(f"Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
print(f"Dementia recall: {dementia_recall:.4f} ({dementia_recall*100:.2f}%)")
print(f"Medical safety: {safety_level}")
print(f"Model file: {OUTPUT_MODEL_PATH}")
print(f"Report file: {OUTPUT_REPORT_PATH}")

if AUTODL_BACKUP:
    print(f"AutoDL backup: {AUTODL_OUTPUT_DIR}")

print(f"End time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
print("TensorFlow Dementia Model completed!")