#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dementia Detection Model Inference
全自动痴呆症检测模型调用代码
"""

import os
import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow import keras
import librosa
import warnings
warnings.filterwarnings('ignore')

# 设置TensorFlow日志级别，抑制警告信息
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
tf.get_logger().setLevel('ERROR')

class DementiaDetector:
    def __init__(self, model_path="D:\\模型开发\\tf_dementia_model.h5"):
        """
        初始化痴呆症检测器
        
        Args:
            model_path: 模型文件路径
        """
        self.model_path = model_path
        self.model = None
        self.class_names = ['Normal', 'MCI', 'Dementia']
        self.load_model()
    
    def load_model(self):
        """加载训练好的模型"""
        try:
            print(f"Loading model from: {self.model_path}")
            self.model = keras.models.load_model(self.model_path)
            print("Model loaded successfully!")
            print(f"Model input shape: {self.model.input_shape}")
            print(f"Model output shape: {self.model.output_shape}")
        except Exception as e:
            print(f"Error loading model: {e}")
            raise
    
    def extract_audio_features(self, audio_path, duration=30):
        """
        从音频文件提取特征
        
        Args:
            audio_path: 音频文件路径
            duration: 音频时长（秒）
        
        Returns:
            features: 提取的特征向量
        """
        try:
            # 加载音频文件
            y, sr = librosa.load(audio_path, duration=duration, sr=22050)
            
            # 提取MFCC特征
            mfccs = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13)
            mfccs_mean = np.mean(mfccs, axis=1)
            mfccs_std = np.std(mfccs, axis=1)
            
            # 提取频谱特征
            spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)
            spectral_rolloff = librosa.feature.spectral_rolloff(y=y, sr=sr)
            spectral_bandwidth = librosa.feature.spectral_bandwidth(y=y, sr=sr)
            zero_crossing_rate = librosa.feature.zero_crossing_rate(y)
            
            # 提取节奏特征
            tempo, beats = librosa.beat.beat_track(y=y, sr=sr)
            
            # 组合所有特征
            features = np.concatenate([
                mfccs_mean,
                mfccs_std,
                [np.mean(spectral_centroids)],
                [np.mean(spectral_rolloff)],
                [np.mean(spectral_bandwidth)],
                [np.mean(zero_crossing_rate)],
                [tempo]
            ])
            
            return features
            
        except Exception as e:
            print(f"Error extracting audio features: {e}")
            return None
    
    def prepare_input_features(self, age=None, gender=None, education_years=None, audio_path=None):
        """
        准备输入特征
        
        Args:
            age: 年龄
            gender: 性别 (M/F 或 1/0)
            education_years: 教育年限
            audio_path: 音频文件路径
        
        Returns:
            features: 准备好的特征向量
        """
        features = []
        
        # 人口统计学特征
        if age is not None:
            features.append(age)
        if gender is not None:
            # 转换性别为数值
            if isinstance(gender, str):
                gender_val = 1 if gender.upper() == 'M' else 0
            else:
                gender_val = gender
            features.append(gender_val)
        if education_years is not None:
            features.append(education_years)
        
        # 音频特征
        if audio_path and os.path.exists(audio_path):
            audio_features = self.extract_audio_features(audio_path)
            if audio_features is not None:
                features.extend(audio_features)
        
        return np.array(features)
    
    def predict(self, age=None, gender=None, education_years=None, audio_path=None):
        """
        进行痴呆症检测预测
        
        Args:
            age: 年龄
            gender: 性别 (M/F 或 1/0)
            education_years: 教育年限
            audio_path: 音频文件路径
        
        Returns:
            prediction: 预测结果字典
        """
        if self.model is None:
            raise ValueError("Model not loaded!")
        
        # 准备输入特征
        features = self.prepare_input_features(age, gender, education_years, audio_path)
        
        if len(features) == 0:
            raise ValueError("No valid features provided!")
        
        # 重塑特征以匹配模型输入
        features = features.reshape(1, -1)
        
        # 进行预测
        predictions = self.model.predict(features, verbose=0)
        predicted_class = np.argmax(predictions[0])
        confidence = np.max(predictions[0])
        
        result = {
            'predicted_class': self.class_names[predicted_class],
            'confidence': float(confidence),
            'probabilities': {
                self.class_names[i]: float(predictions[0][i]) 
                for i in range(len(self.class_names))
            },
            'features_used': len(features[0])
        }
        
        return result
    
    def batch_predict(self, data_list):
        """
        批量预测
        
        Args:
            data_list: 包含多个样本数据的列表
        
        Returns:
            results: 预测结果列表
        """
        results = []
        for i, data in enumerate(data_list):
            try:
                result = self.predict(**data)
                result['sample_id'] = i
                results.append(result)
            except Exception as e:
                print(f"Error predicting sample {i}: {e}")
                results.append({
                    'sample_id': i,
                    'error': str(e)
                })
        
        return results

def main():
    """主函数 - 演示模型使用"""
    print("=== Dementia Detection Model Inference ===")
    
    # 初始化检测器
    detector = DementiaDetector()
    
    # 示例1: 使用人口统计学数据
    print("\n--- Example 1: Demographic data only ---")
    result1 = detector.predict(age=75, gender='M', education_years=12)
    print(f"Prediction: {result1['predicted_class']}")
    print(f"Confidence: {result1['confidence']:.3f}")
    print(f"Probabilities: {result1['probabilities']}")
    
    # 示例2: 使用音频文件（如果存在）
    audio_file = "D:\\模型开发\\sample_audio.wav"  # 替换为实际音频文件路径
    if os.path.exists(audio_file):
        print(f"\n--- Example 2: With audio file ---")
        result2 = detector.predict(age=68, gender='F', education_years=16, audio_path=audio_file)
        print(f"Prediction: {result2['predicted_class']}")
        print(f"Confidence: {result2['confidence']:.3f}")
        print(f"Features used: {result2['features_used']}")
    else:
        print(f"\n--- Audio file not found: {audio_file} ---")
    
    # 示例3: 批量预测
    print("\n--- Example 3: Batch prediction ---")
    batch_data = [
        {'age': 70, 'gender': 'M', 'education_years': 14},
        {'age': 65, 'gender': 'F', 'education_years': 18},
        {'age': 80, 'gender': 'M', 'education_years': 10}
    ]
    
    batch_results = detector.batch_predict(batch_data)
    for result in batch_results:
        if 'error' not in result:
            print(f"Sample {result['sample_id']}: {result['predicted_class']} "
                  f"(confidence: {result['confidence']:.3f})")
        else:
            print(f"Sample {result['sample_id']}: Error - {result['error']}")

if __name__ == "__main__":
    main()
