#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频痴呆症检测工具
格式: python audio_predict.py 年龄 性别 教育年限 音频文件路径
示例: python audio_predict.py 90 M 20 D:\\audio\\sample.wav
"""

import sys
import os
import warnings
warnings.filterwarnings('ignore')
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

def main():
    print("=== 音频痴呆症检测工具 ===")
    
    # 检查参数数量
    if len(sys.argv) != 5:
        print("\n使用方法:")
        print("python audio_predict.py 年龄 性别 教育年限 音频文件路径")
        print("\n示例:")
        print("python audio_predict.py 90 M 20 D:\\audio\\sample.wav")
        print("python audio_predict.py 75 F 16 C:\\Users\\<USER>\n参数说明:")
        print("年龄: 数字 (如 90)")
        print("性别: M 或 F")
        print("教育年限: 数字 (如 20)")
        print("音频文件路径: 完整的 .wav 文件路径")
        return
    
    try:
        # 解析命令行参数
        age = int(sys.argv[1])
        gender = sys.argv[2].upper()
        education_years = int(sys.argv[3])
        audio_file = sys.argv[4]
        
        # 验证性别参数
        if gender not in ['M', 'F']:
            print(f"[ERROR] 性别必须是 M 或 F，您输入的是: {gender}")
            return

        # 验证音频文件
        if not os.path.exists(audio_file):
            print(f"[ERROR] 音频文件不存在: {audio_file}")
            print("请检查文件路径是否正确")
            return

        if not audio_file.lower().endswith('.wav'):
            print(f"[WARNING] 推荐使用 .wav 格式，当前文件: {audio_file}")

        print(f"\n[INFO] 输入信息:")
        print(f"   年龄: {age}岁")
        print(f"   性别: {gender}")
        print(f"   教育年限: {education_years}年")
        print(f"   音频文件: {audio_file}")
        print(f"   文件大小: {os.path.getsize(audio_file) / 1024:.1f} KB")

        print(f"\n[PROCESSING] 正在加载模型和分析音频...")
        
        # 导入预测模块
        from easy_predict import detailed_predict
        
        # 进行预测
        result = detailed_predict(
            age=age, 
            gender=gender, 
            education_years=education_years, 
            audio_file=audio_file
        )
        
        # 输出结果
        print(f"\n=== 预测结果 ===")
        print(f"预测类别: {result['predicted_class']}")
        print(f"置信度: {result['confidence']:.1%}")

        print(f"\n详细概率:")
        for class_name, prob in result['probabilities'].items():
            bar = "=" * int(prob * 30)  # 进度条
            print(f"  {class_name:8}: {prob:.6f} {bar}")

        print(f"\n音频特征: {'[YES] 已使用' if result['has_audio'] else '[NO] 未使用'}")
        print(f"输入维度: {result['input_shapes']}")

        # 结果解释
        print(f"\n=== 结果解释 ===")
        if result['predicted_class'] == 'Normal':
            print("[NORMAL] 认知功能正常")
            print("   建议: 继续保持健康的生活方式")
        elif result['predicted_class'] == 'MCI':
            print("[MCI] 轻度认知障碍")
            print("   建议: 建议咨询医生进行进一步评估")
        elif result['predicted_class'] == 'Dementia':
            print("[DEMENTIA] 痴呆症")
            print("   建议: 强烈建议尽快就医诊断")

        print(f"\n注意: 此结果仅供参考，不能替代专业医学诊断")
        
    except ValueError as e:
        print(f"[ERROR] 参数错误: {e}")
        print("年龄和教育年限必须是数字")
    except Exception as e:
        print(f"[ERROR] 预测错误: {e}")
        import traceback
        traceback.print_exc()

def test_without_audio():
    """测试不使用音频的情况"""
    print("\n=== 对比测试 (不使用音频) ===")
    try:
        from easy_predict import detailed_predict
        
        # 使用相同的人口统计学数据，但不使用音频
        result_no_audio = detailed_predict(
            age=int(sys.argv[1]), 
            gender=sys.argv[2].upper(), 
            education_years=int(sys.argv[3])
        )
        
        print(f"不使用音频的预测: {result_no_audio['predicted_class']} (置信度: {result_no_audio['confidence']:.1%})")
        
    except:
        pass

if __name__ == "__main__":
    main()
    
    # 如果有足够的参数，进行对比测试
    if len(sys.argv) == 5:
        test_without_audio()
