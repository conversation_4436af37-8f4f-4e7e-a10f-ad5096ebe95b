#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的痴呆症检测模型测试脚本
"""

import os
import warnings
warnings.filterwarnings('ignore')
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

from dementia_model_inference import DementiaDetector

def test_model():
    """测试模型功能"""
    print("=== 痴呆症检测模型测试 ===")
    
    # 初始化检测器
    detector = DementiaDetector()
    
    # 测试案例1: 只使用人口统计学数据
    print("\n【测试1】仅使用人口统计学数据:")
    result1 = detector.predict(age=75, gender='M', education_years=12)
    print(f"预测结果: {result1['predicted_class']}")
    print(f"置信度: {result1['confidence']:.3f}")
    print(f"各类别概率:")
    for class_name, prob in result1['probabilities'].items():
        print(f"  {class_name}: {prob:.6f}")
    
    # 测试案例2: 不同年龄组
    print("\n【测试2】不同年龄组测试:")
    test_cases = [
        {'age': 45, 'gender': 'F', 'education_years': 16, 'desc': '45岁女性，高学历'},
        {'age': 65, 'gender': 'M', 'education_years': 12, 'desc': '65岁男性，中等学历'},
        {'age': 80, 'gender': 'F', 'education_years': 8, 'desc': '80岁女性，低学历'},
        {'age': 85, 'gender': 'M', 'education_years': 6, 'desc': '85岁男性，低学历'}
    ]
    
    for i, case in enumerate(test_cases):
        result = detector.predict(age=case['age'], gender=case['gender'], education_years=case['education_years'])
        print(f"  案例{i+1} - {case['desc']}: {result['predicted_class']} (置信度: {result['confidence']:.3f})")
    
    # 测试案例3: 如果有音频文件
    audio_file = "D:\\模型开发\\sample_audio.wav"
    if os.path.exists(audio_file):
        print(f"\n【测试3】包含音频特征:")
        result3 = detector.predict(age=70, gender='M', education_years=14, audio_path=audio_file)
        print(f"预测结果: {result3['predicted_class']}")
        print(f"置信度: {result3['confidence']:.3f}")
        print(f"使用了音频特征: {result3['has_audio']}")
    else:
        print(f"\n【测试3】音频文件不存在: {audio_file}")
        print("如需测试音频功能，请将音频文件放置在该路径")
    
    print("\n=== 测试完成 ===")

def quick_predict(age, gender, education_years, audio_path=None):
    """快速预测函数"""
    detector = DementiaDetector()
    result = detector.predict(age=age, gender=gender, education_years=education_years, audio_path=audio_path)
    
    print(f"\n=== 快速预测结果 ===")
    print(f"输入信息: 年龄={age}, 性别={gender}, 教育年限={education_years}")
    if audio_path:
        print(f"音频文件: {audio_path}")
    print(f"预测结果: {result['predicted_class']}")
    print(f"置信度: {result['confidence']:.3f}")
    print(f"详细概率:")
    for class_name, prob in result['probabilities'].items():
        print(f"  {class_name}: {prob:.6f}")
    
    return result

if __name__ == "__main__":
    # 运行测试
    test_model()
    
    # 快速预测示例
    print("\n" + "="*50)
    quick_predict(age=72, gender='F', education_years=14)
