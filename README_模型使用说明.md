# 痴呆症检测模型使用说明

## 模型信息
- **模型文件**: `D:\模型开发\tf_dementia_model.h5`
- **模型类型**: TensorFlow/Keras 多输入神经网络
- **预测类别**: Normal (正常) / MCI (轻度认知障碍) / Dementia (痴呆症)
- **输入特征**: 人口统计学数据 + 音频特征（可选）

## 文件说明

### 1. `dementia_model_inference.py` - 完整功能模块
包含完整的 `DementiaDetector` 类，支持：
- 模型加载和预测
- 音频特征提取
- 批量预测
- 详细结果分析

### 2. `simple_dementia_test.py` - 测试脚本
用于测试模型功能，包含多种测试案例

### 3. `easy_predict.py` - 简易调用接口
最简单的调用方式，提供两个主要函数：
- `predict_dementia()` - 快速预测
- `detailed_predict()` - 详细预测

## 快速开始

### 方法1: 使用简易接口（推荐）

```python
from easy_predict import predict_dementia, detailed_predict

# 基础预测
result = predict_dementia(age=75, gender='M', education_years=12)
print(result)  # 输出: Normal (置信度: 100.0%)

# 详细预测
detailed_result = detailed_predict(age=75, gender='M', education_years=12)
print(detailed_result['predicted_class'])  # Normal
print(detailed_result['confidence'])       # 1.0
print(detailed_result['probabilities'])    # 各类别概率
```

### 方法2: 直接使用类

```python
from dementia_model_inference import DementiaDetector

# 初始化检测器
detector = DementiaDetector()

# 进行预测
result = detector.predict(age=75, gender='M', education_years=12)
print(f"预测结果: {result['predicted_class']}")
print(f"置信度: {result['confidence']:.3f}")
```

## 参数说明

### 必需参数
- **age**: 年龄（数字，如 75）
- **gender**: 性别（字符串 'M' 或 'F'，或数字 1/0）
- **education_years**: 教育年限（数字，如 12）

### 可选参数
- **audio_path**: 音频文件路径（字符串，支持 .wav 格式）

## 输出格式

### 基础预测输出
```
"Normal (置信度: 100.0%)"
```

### 详细预测输出
```python
{
    'predicted_class': 'Normal',
    'confidence': 1.0,
    'probabilities': {
        'Normal': 1.0,
        'MCI': 0.0,
        'Dementia': 0.0
    },
    'input_shapes': [(1, 3), (1, 10), (1, 13), (1, 9), (1, 3)],
    'has_audio': False
}
```

## 使用示例

### 示例1: 不同年龄组测试
```python
from easy_predict import predict_dementia

# 测试不同年龄组
test_cases = [
    (45, 'F', 16),  # 45岁女性，高学历
    (65, 'M', 12),  # 65岁男性，中等学历
    (80, 'F', 8),   # 80岁女性，低学历
    (85, 'M', 6)    # 85岁男性，低学历
]

for age, gender, edu in test_cases:
    result = predict_dementia(age, gender, edu)
    print(f"{age}岁{gender}: {result}")
```

### 示例2: 包含音频特征
```python
# 如果有音频文件
audio_file = "D:\\模型开发\\sample_audio.wav"
result = predict_dementia(
    age=70, 
    gender='M', 
    education_years=14, 
    audio_file=audio_file
)
print(result)
```

### 示例3: 批量预测
```python
from dementia_model_inference import DementiaDetector

detector = DementiaDetector()

batch_data = [
    {'age': 70, 'gender': 'M', 'education_years': 14},
    {'age': 65, 'gender': 'F', 'education_years': 18},
    {'age': 80, 'gender': 'M', 'education_years': 10}
]

results = detector.batch_predict(batch_data)
for result in results:
    print(f"样本 {result['sample_id']}: {result['predicted_class']}")
```

## 模型特征

### 输入维度
1. **人口统计学特征** (3维): 年龄、性别、教育年限
2. **声学特征** (10维): 频谱质心、频谱滚降等
3. **MFCC特征** (13维): 梅尔频率倒谱系数
4. **频谱特征** (9维): 频谱对比度、调性等
5. **节奏特征** (3维): 节拍、起始密度等

### 输出类别
- **Normal**: 正常认知功能
- **MCI**: 轻度认知障碍
- **Dementia**: 痴呆症

## 注意事项

1. **模型路径**: 确保模型文件在 `D:\模型开发\tf_dementia_model.h5`
2. **音频格式**: 支持 .wav 格式，推荐 22050Hz 采样率
3. **依赖包**: 需要安装 tensorflow, librosa, numpy, pandas
4. **性能**: 首次加载模型需要几秒钟，后续预测很快

## 安装依赖

```bash
pip install tensorflow librosa pandas numpy
```

## 运行测试

```bash
# 运行完整测试
python simple_dementia_test.py

# 运行简易接口测试
python easy_predict.py
```

## 常见问题

### Q: 模型预测结果都是 Normal？
A: 这可能是因为：
1. 模型训练数据分布不均
2. 输入特征需要标准化
3. 模型需要重新训练或调优

### Q: 音频特征提取失败？
A: 检查：
1. 音频文件格式是否正确
2. 文件路径是否存在
3. librosa 是否正确安装

### Q: 如何提高预测准确性？
A: 建议：
1. 提供音频文件以获得更多特征
2. 确保输入数据的准确性
3. 考虑模型重新训练
