#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最简单的痴呆症检测模型调用接口
"""

import os
import warnings
warnings.filterwarnings('ignore')
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

from dementia_model_inference import DementiaDetector

# 全局检测器实例
detector = None

def init_model():
    """初始化模型（只需调用一次）"""
    global detector
    if detector is None:
        print("正在加载模型...")
        detector = DementiaDetector()
        print("模型加载完成！")
    return detector

def predict_dementia(age, gender, education_years, audio_file=None):
    """
    一键预测痴呆症
    
    参数:
        age: 年龄 (数字)
        gender: 性别 ('M' 或 'F')
        education_years: 教育年限 (数字)
        audio_file: 音频文件路径 (可选)
    
    返回:
        预测结果字符串
    """
    # 确保模型已加载
    if detector is None:
        init_model()
    
    # 进行预测
    result = detector.predict(
        age=age, 
        gender=gender, 
        education_years=education_years, 
        audio_path=audio_file
    )
    
    # 格式化输出
    prediction = result['predicted_class']
    confidence = result['confidence']
    
    return f"{prediction} (置信度: {confidence:.1%})"

def detailed_predict(age, gender, education_years, audio_file=None):
    """
    详细预测（返回所有概率）
    
    参数:
        age: 年龄
        gender: 性别 ('M' 或 'F')  
        education_years: 教育年限
        audio_file: 音频文件路径 (可选)
    
    返回:
        详细结果字典
    """
    if detector is None:
        init_model()
    
    return detector.predict(
        age=age,
        gender=gender, 
        education_years=education_years,
        audio_path=audio_file
    )

# 使用示例
if __name__ == "__main__":
    print("=== 痴呆症检测模型 - 简易调用接口 ===\n")
    
    # 示例1: 基础预测
    print("示例1 - 基础预测:")
    result1 = predict_dementia(age=75, gender='M', education_years=12)
    print(f"75岁男性，12年教育: {result1}\n")
    
    # 示例2: 多个案例
    print("示例2 - 批量测试:")
    test_cases = [
        (45, 'F', 16, "45岁女性，高学历"),
        (65, 'M', 12, "65岁男性，中等学历"), 
        (80, 'F', 8, "80岁女性，低学历"),
        (85, 'M', 6, "85岁男性，低学历")
    ]
    
    for age, gender, edu, desc in test_cases:
        result = predict_dementia(age, gender, edu)
        print(f"{desc}: {result}")
    
    print("\n示例3 - 详细预测:")
    detailed_result = detailed_predict(age=70, gender='F', education_years=14)
    print(f"预测: {detailed_result['predicted_class']}")
    print(f"置信度: {detailed_result['confidence']:.3f}")
    print("各类别概率:")
    for class_name, prob in detailed_result['probabilities'].items():
        print(f"  {class_name}: {prob:.6f}")
    
    print(f"\n模型输入维度: {detailed_result['input_shapes']}")
    print(f"使用音频特征: {detailed_result['has_audio']}")
    
    print("\n=== 调用完成 ===")
    print("\n使用方法:")
    print("1. 基础预测: predict_dementia(age, gender, education_years)")
    print("2. 详细预测: detailed_predict(age, gender, education_years)")
    print("3. 包含音频: predict_dementia(age, gender, education_years, 'audio.wav')")
