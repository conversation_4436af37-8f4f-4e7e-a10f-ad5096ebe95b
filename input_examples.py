#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
痴呆症检测模型 - 输入格式范例
"""

import os
import warnings
warnings.filterwarnings('ignore')
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

from easy_predict import predict_dementia, detailed_predict

def test_input_formats():
    """测试各种输入格式"""
    print("=== 痴呆症检测模型 - 输入格式范例 ===\n")
    
    # 范例1: 您提到的格式 90,M,12
    print("【范例1】90岁男性，12年教育:")
    result1 = predict_dementia(age=90, gender='M', education_years=12)
    print(f"输入: 90,M,12")
    print(f"结果: {result1}\n")
    
    # 范例2: 各种年龄段
    print("【范例2】不同年龄段测试:")
    test_cases = [
        (45, 'F', 16, "45,F,16 - 中年女性"),
        (60, 'M', 14, "60,M,14 - 老年前期男性"),
        (75, 'F', 10, "75,F,10 - 老年女性"),
        (85, 'M', 8, "85,M,8 - 高龄男性"),
        (90, 'F', 6, "90,F,6 - 超高龄女性")
    ]
    
    for age, gender, edu, desc in test_cases:
        result = predict_dementia(age, gender, edu)
        print(f"输入: {age},{gender},{edu} -> {result}")
    
    print("\n【范例3】性别的不同输入方式:")
    # 测试性别的不同输入格式
    gender_formats = [
        ('M', "大写M"),
        ('m', "小写m"), 
        ('F', "大写F"),
        ('f', "小写f"),
        (1, "数字1(男)"),
        (0, "数字0(女)")
    ]
    
    for gender, desc in gender_formats:
        result = predict_dementia(age=70, gender=gender, education_years=12)
        print(f"70,{gender},12 ({desc}) -> {result}")
    
    print("\n【范例4】详细预测结果:")
    detailed_result = detailed_predict(age=90, gender='M', education_years=12)
    print(f"输入: 90,M,12")
    print(f"预测类别: {detailed_result['predicted_class']}")
    print(f"置信度: {detailed_result['confidence']:.3f}")
    print("各类别概率:")
    for class_name, prob in detailed_result['probabilities'].items():
        print(f"  {class_name}: {prob:.6f}")

def quick_test(age, gender, education_years):
    """快速测试函数"""
    print(f"\n=== 快速测试: {age},{gender},{education_years} ===")
    result = predict_dementia(age=age, gender=gender, education_years=education_years)
    print(f"预测结果: {result}")
    return result

def batch_test():
    """批量测试常见输入"""
    print("\n=== 批量测试常见输入格式 ===")
    
    # 常见的输入组合
    inputs = [
        (50, 'M', 16),   # 50,M,16
        (65, 'F', 12),   # 65,F,12  
        (70, 'M', 14),   # 70,M,14
        (75, 'F', 10),   # 75,F,10
        (80, 'M', 8),    # 80,M,8
        (85, 'F', 6),    # 85,F,6
        (90, 'M', 12),   # 90,M,12 (您的例子)
        (95, 'F', 4)     # 95,F,4
    ]
    
    print("输入格式: 年龄,性别,教育年限 -> 预测结果")
    print("-" * 50)
    
    for age, gender, edu in inputs:
        result = predict_dementia(age, gender, edu)
        print(f"{age},{gender},{edu:2d} -> {result}")

if __name__ == "__main__":
    # 运行所有测试
    test_input_formats()
    
    # 您的具体例子
    print("\n" + "="*60)
    quick_test(90, 'M', 12)
    
    # 批量测试
    batch_test()
    
    print("\n=== 使用说明 ===")
    print("输入格式: predict_dementia(年龄, '性别', 教育年限)")
    print("年龄: 任意数字 (如 90)")
    print("性别: 'M'/'F' 或 'm'/'f' 或 1/0")
    print("教育年限: 任意数字 (如 12)")
    print("\n示例调用:")
    print("predict_dementia(90, 'M', 12)")
    print("predict_dementia(75, 'F', 16)")
    print("predict_dementia(65, 1, 14)  # 1代表男性")
