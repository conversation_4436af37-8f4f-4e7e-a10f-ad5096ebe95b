import pandas as pd
import numpy as np
import json
import pickle
import warnings
import time
import os
from sklearn.model_selection import GridSearchCV
from sklearn.metrics import classification_report, accuracy_score, f1_score
from sklearn.preprocessing import LabelEncoder
from sklearn.ensemble import VotingClassifier, RandomForestClassifier
from sklearn.linear_model import LogisticRegression

warnings.filterwarnings('ignore')

print("Starting optimized trainer")
print(f"Start time: {time.strftime('%Y-%m-%d %H:%M:%S')}")

# Configuration - modify these paths
TRAIN_DATA_PATH = "train_set_scaled.csv"
VALIDATION_DATA_PATH = "validation_set_scaled.csv"
FEATURE_INFO_PATH = "feature_info.json"
LABEL_ENCODER_PATH = "label_encoder.pkl"
OUTPUT_MODEL_PATH = "optimized_dementia_model.pkl"
OUTPUT_REPORT_PATH = "training_report.json"
# AutoDL目录配置
AUTODL_OUTPUT_DIR = "/root/autodl-tmp/models"
AUTODL_BACKUP = True  # 设置为True启用AutoDL备份

print(f"Train data: {TRAIN_DATA_PATH}")
print(f"Validation data: {VALIDATION_DATA_PATH}")
print(f"Output model: {OUTPUT_MODEL_PATH}")
if AUTODL_BACKUP:
    print(f"AutoDL backup directory: {AUTODL_OUTPUT_DIR}")
    # 确保AutoDL目录存在
    try:
        os.makedirs(AUTODL_OUTPUT_DIR, exist_ok=True)
        print(f"AutoDL directory created/verified: {AUTODL_OUTPUT_DIR}")
    except Exception as e:
        print(f"Warning: Could not create AutoDL directory: {e}")
        AUTODL_BACKUP = False